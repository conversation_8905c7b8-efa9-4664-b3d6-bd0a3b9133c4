[{"title": "知识库目录", "url": "ixei5xcutslq3fx2", "doc_id": 217484477, "children": []}, {"title": "SAAS业务", "url": "", "doc_id": "", "children": [{"title": "客无忧", "url": "qcbnh04qum7hlxyf", "doc_id": 228103772, "children": [{"title": "客无忧生意通开通流程", "url": "iibnkp8iorv1q5h7", "doc_id": 228105025, "children": []}, {"title": "客无忧标准版开通流程", "url": "oi98igv8k8fh9yvn", "doc_id": 228103936, "children": []}]}, {"title": "纳客", "url": "lz989f9bvxkhdkvt", "doc_id": 228062033, "children": [{"title": "纳客营销功能开通流程", "url": "noyaficagiigcakn", "doc_id": 228102731, "children": []}, {"title": "纳客餐饮版开通流程", "url": "gcqv5zmix926eght", "doc_id": 228102084, "children": []}, {"title": "纳客行业版开通流程", "url": "gtr9akfxye9fwgko", "doc_id": 228100535, "children": []}, {"title": "纳客短信服务通知服务开通流程", "url": "xec5f0l516n4vmks", "doc_id": 228066558, "children": []}, {"title": "纳客小程序开通流程", "url": "wk3c98g1mkmpgyuk", "doc_id": 228064738, "children": []}, {"title": "纳客团购开通流程", "url": "rbnp62yqg72za5ic", "doc_id": 228068252, "children": []}, {"title": "纳客外卖开通流程", "url": "humgngk8yevdasml", "doc_id": 228062125, "children": []}, {"title": "纳客扫码点餐支付配置流程", "url": "ch597knw0aedb4pr", "doc_id": 228063922, "children": []}]}, {"title": "惠管家", "url": "", "doc_id": "", "children": [{"title": "惠管家标准版开通流程", "url": "nhgx261zlai3kxwr", "doc_id": 228068937, "children": []}, {"title": "惠管家小程序开通流程", "url": "bmg8h015elgy88id", "doc_id": 227981090, "children": []}, {"title": "惠管家外卖+团购开通流程", "url": "brehv9hzimeetfr9", "doc_id": 227961228, "children": []}, {"title": "惠管家AI功能开通流程", "url": "th27btpyrdbiuo8h", "doc_id": 227947563, "children": []}, {"title": "惠管家惠点点电子会员开通流程", "url": "ze3y95honlnl1l7g", "doc_id": 227286632, "children": []}, {"title": "惠管家扫码点餐支付配置流程", "url": "gb0ucqcef5ww8hkg", "doc_id": 227129459, "children": []}]}]}, {"title": "公司资料—首展公司相关资质", "url": "ckku0lyzgtuo4zsv", "doc_id": 228256889, "children": [{"title": "首展荣誉", "url": "uwq2eayr9zoifzt8", "doc_id": 228276371, "children": []}, {"title": "友店营业执照", "url": "dwpguzp3a9crzivn", "doc_id": 228274845, "children": []}, {"title": "首展营业执照", "url": "ngc9kcqwsk7zppne", "doc_id": 228274147, "children": []}, {"title": "付呗logo", "url": "cfggnugmb34nflvu", "doc_id": 228273109, "children": []}, {"title": "商户和代理商合同", "url": "tm2d800ut95u9543", "doc_id": 229028866, "children": []}]}, {"title": "硬件", "url": "px6nwqdvff4c8fau", "doc_id": 224349310, "children": [{"title": "外卡业务", "url": "", "doc_id": "", "children": [{"title": "外卡开通流程", "url": "apmi865frk646d0w", "doc_id": 229404203, "children": []}, {"title": "外卡通道", "url": "onv8du8v61xdgcab", "doc_id": 229224309, "children": []}, {"title": "费率修改", "url": "wniq7yo5pu7x82qy", "doc_id": 229410577, "children": []}]}, {"title": "硬件功能汇总表", "url": "kq1f1hx2aygkpfmn", "doc_id": 225575989, "children": []}, {"title": "4G移动流量卡常见QA", "url": "ip7xl2kgqlm5xx3x", "doc_id": 226606509, "children": []}, {"title": "硬件打印纸规格", "url": "pnds9gns4yh87tnq", "doc_id": 226461005, "children": []}, {"title": "终端硬件转网流程", "url": "malgg2bd1sgkin5g", "doc_id": 225652450, "children": []}, {"title": "硬件密码（联迪A8，云pos，T9，商米POS）", "url": "ewhdbxxfm1grkvup", "doc_id": 225628709, "children": []}, {"title": "机器维保地址", "url": "gveuw2e6r4p0euh4", "doc_id": 225746049, "children": []}, {"title": "收银机", "url": "sy21dmfbsgxnymg0", "doc_id": 227259654, "children": [{"title": "付呗M600单双屏收银机", "url": "wwviqxknq8xm2tz1", "doc_id": 227268185, "children": []}, {"title": "DM一体秤收银机", "url": "ekph8ikmsq7viaek", "doc_id": 227269135, "children": []}, {"title": "虎风一体秤收银机", "url": "flx9t5yson2p8kc5", "doc_id": 227267640, "children": []}, {"title": "智崎一体秤收银机", "url": "xuutekrkmtgx9dnf", "doc_id": 227261583, "children": []}, {"title": "T3单屏收银机", "url": "irxlekth2rb3me42", "doc_id": 227259683, "children": []}]}, {"title": "如意设备", "url": "krtxy98uzlk46n38", "doc_id": 226643518, "children": [{"title": "如意报错", "url": "zsm23qlnd6ef0wmo", "doc_id": 226645668, "children": [{"title": "如意开机显示“系统故障请联系服务商维修”", "url": "ovntovcevi1lkwkq", "doc_id": 227598795, "children": []}, {"title": "如意充电开不了机", "url": "mbkcure32gggkv53", "doc_id": 227556475, "children": []}, {"title": "如意门店名称修改后，如意设备不同步", "url": "xpuiuetywzglfbb2", "doc_id": 226646114, "children": []}, {"title": "如意绑定提示“设备不存在”", "url": "bnulfg6wbms5m5uf", "doc_id": 226645937, "children": []}, {"title": "如意绑定提示“pid类型不支持当前场景”", "url": "fp3gniphxevocay6", "doc_id": 226645833, "children": []}, {"title": "如意绑定提示“商户分级未到达M2”", "url": "gsth4f539qccwh4s", "doc_id": 226645689, "children": []}]}, {"title": "如意功能", "url": "epeaihxw8pogqyic", "doc_id": 226645647, "children": [{"title": "如意设备收款可以打印商品明细小票吗", "url": "hvoakb3mtoshx2fy", "doc_id": 226646511, "children": []}, {"title": "如意设备怎么管理商品信息", "url": "pwxrogpf82wk0h9l", "doc_id": 226646407, "children": []}, {"title": "如意设备支持什么网络", "url": "itbtzcxtscvs7evg", "doc_id": 226643955, "children": []}, {"title": "如意设备怎么退款", "url": "pla5cmo4aep2gms7", "doc_id": 226644865, "children": []}, {"title": "如意设备绑定流程", "url": "fqk78ldl6k3di1ge", "doc_id": 226643569, "children": []}, {"title": "如意设备使用流程图", "url": "rt6zglddf1ch2g8r", "doc_id": 226645019, "children": []}]}]}, {"title": "蜻蜓刷脸设备", "url": "tywsvtb5a97v522m", "doc_id": 226639100, "children": [{"title": "刷脸设备独立模式收款显示“请商家联系付呗客服(9999)”", "url": "kntz3xg1ulgfnxgy", "doc_id": 229569745, "children": []}, {"title": "刷脸设备怎么维保", "url": "ke4eolpqcfw6xanw", "doc_id": 229412063, "children": []}, {"title": "刷脸设备连接收银机模式收款显示“收银异常，请收银员核实支付结果”", "url": "hvolw5zdtcvxyiv9", "doc_id": 228453323, "children": []}, {"title": "刷脸设备独立模式自助购流程", "url": "gh1g1ob11wrg1non", "doc_id": 227155859, "children": []}, {"title": "刷脸设备独立模式交接班功能", "url": "oniwbb3gfidvxpct", "doc_id": 227154797, "children": []}, {"title": "刷脸设备独立模式支付结果倒计时设置", "url": "io5dak24acwep177", "doc_id": 227154461, "children": []}, {"title": "刷脸设备独立模式怎么退款", "url": "mihvu31qybmg7zmm", "doc_id": 227154232, "children": []}, {"title": "刷脸设备独立模式怎么会员收款", "url": "mkwfifvsze51zhd6", "doc_id": 227152555, "children": []}, {"title": "刷脸设备独立模式怎么播报和打印其他通道订单", "url": "nqzy5w19xnaav20n", "doc_id": 227151712, "children": []}, {"title": "刷脸设备独立模式怎么设置设备密码", "url": "iz70l1tf6omuhvx9", "doc_id": 227150700, "children": []}, {"title": "刷脸设备独立模式收款可以打印小票吗", "url": "gliabgagwokcrrh5", "doc_id": 226646629, "children": []}, {"title": "刷脸设备怎么修改海报", "url": "uz05sv3nlu4rzmdq", "doc_id": 226643113, "children": []}, {"title": "刷脸设备怎么连接收银机使用", "url": "hkvtk6vszdreerli", "doc_id": 226642529, "children": []}, {"title": "刷脸设备支持什么网络，怎么充值流量", "url": "veox2fw2muau6c7h", "doc_id": 226641700, "children": []}, {"title": "刷脸设备推送小程序", "url": "tzgcl2is9h4p91l7", "doc_id": 226639989, "children": []}, {"title": "刷脸设备绑定流程（独立模式）", "url": "ez0istgrbtnuf1xl", "doc_id": *********, "children": []}, {"title": "刷脸设备解绑流程", "url": "dabfrxsa0ctd1c1f", "doc_id": *********, "children": []}]}, {"title": "打印机", "url": "bwdgoh0mzh7anadt", "doc_id": *********, "children": [{"title": "飞蛾云打印机", "url": "xushlnn2n34ogq5x", "doc_id": *********, "children": [{"title": "飞蛾云打印机绑定流程", "url": "pnko897ur8ux1pzz", "doc_id": *********, "children": []}]}, {"title": "芯烨打印机", "url": "hrd06tfn0wgrr7p3", "doc_id": *********, "children": [{"title": "芯烨USB打印机", "url": "zbw4im200l14fcr1", "doc_id": *********, "children": []}, {"title": "芯烨标签打印机", "url": "xad8cqsopz3ca4cr", "doc_id": *********, "children": []}, {"title": "芯烨网口打印机", "url": "spx4timiuqsnsrfs", "doc_id": *********, "children": []}]}, {"title": "智网云打印机", "url": "nbcpozg62rtzotg6", "doc_id": *********, "children": [{"title": "智网云打印机如何充值流量", "url": "sm9n4tdtg4gyb03p", "doc_id": *********, "children": []}, {"title": "智网云打印机功能介绍", "url": "zeluod1img8extxs", "doc_id": *********, "children": []}, {"title": "智网云打印机如何对接收银机使用", "url": "vhotb9detnwssitk", "doc_id": *********, "children": []}]}, {"title": "365打印机", "url": "ez4ek635wwi1p5b2", "doc_id": *********, "children": [{"title": "365打印机流量卡还有流量不出票", "url": "zef0sgg0x1fdda8t", "doc_id": *********, "children": []}, {"title": "365打印机功能介绍", "url": "mmeuo2v0q5n9ftgw", "doc_id": 226588277, "children": []}, {"title": "365打印机流量如何充值", "url": "mtt40spr5ugp0ohu", "doc_id": 226586387, "children": []}, {"title": "365打印机绑定流程", "url": "gcviuxqftgf8gcvr", "doc_id": 226584151, "children": []}]}, {"title": "博实结4G云打印机", "url": "kkpqzu0q82lp9eka", "doc_id": 225746155, "children": [{"title": "博实结云打印机之前绑定过美团和饿了么，现在美团饿了么重新注册了账号，需要怎么操作才能绑定", "url": "gcf8atvc5cek51w9", "doc_id": 227292978, "children": []}, {"title": "博实结云打印机坏了怎么处理", "url": "avv1dtsqlfz9d8ou", "doc_id": 225930214, "children": []}, {"title": "博实结云打印机打印出来空白或者打印字迹模糊", "url": "xv1plpt35kg7yyr3", "doc_id": 225748297, "children": []}, {"title": "博实结云打印机无法打印小票", "url": "sk19y8isbs2dvgrw", "doc_id": 225747762, "children": []}, {"title": "博实结云打印机打印一半没纸了，重新装纸上去是否口自动打印剩下订单", "url": "wsvu1dmh57bv1u06", "doc_id": 225747671, "children": []}, {"title": "博实结云打印机可以同时打印前厅和后厨小票吗", "url": "uuwqgth8gyissa8v", "doc_id": 225747506, "children": []}, {"title": "博实结云打印机流量怎么充值", "url": "tu1q5zb5cmk1s52l", "doc_id": 225747273, "children": []}, {"title": "博实结云打印机销卡了怎么处理", "url": "spighf97bi8g68in", "doc_id": 225747232, "children": []}, {"title": "博实结云打印机可以USB模式使用吗", "url": "ygum0hqmsqxkcr6o", "doc_id": 225747126, "children": []}, {"title": "博实结云打印机怎么解绑第三方外卖平台", "url": "rolxrkusa2vm9b0c", "doc_id": 225747083, "children": []}, {"title": "博实结云打印机如何打印和播报其他通道订单吗", "url": "uflhs5832hb6nu5g", "doc_id": 225746981, "children": []}, {"title": "博实结云打印机是否支持打印标签小票", "url": "gccsyibi9k0xtxsh", "doc_id": 225746908, "children": []}, {"title": "博实结云打印机怎么连接WIFI", "url": "ylt0gf0d4dmys4l4", "doc_id": 225746817, "children": []}, {"title": "博实结云打印机怎么切换网络", "url": "ym3t2k8aoxs9h0u8", "doc_id": 225746791, "children": []}, {"title": "博实结云打印机绑定及解绑流程", "url": "cb18ssye31tapii7", "doc_id": 225746270, "children": []}, {"title": "博实结云打印机怎么看带不带自动切纸功能", "url": "lc052t5ax0zs9i2m", "doc_id": 225746260, "children": []}, {"title": "博实结云打印机怎么对接外卖平台", "url": "ma4eha34vnz3pnve", "doc_id": 225746169, "children": []}]}]}, {"title": "音箱", "url": "owd0nrebazg73rbf", "doc_id": 226470973, "children": [{"title": "音箱播报逻辑", "url": "bu23us2e618718ok", "doc_id": 225752087, "children": []}, {"title": "恒科码牌音箱", "url": "yyf5rea7hyholfp9", "doc_id": 227582909, "children": [{"title": "查看流量显示销停机超过三个月，充值需500元", "url": "viaa02mikh4dyyc8", "doc_id": 227964733, "children": []}, {"title": "恒科码牌音箱查询有流量的情况连接不上网络", "url": "czhovgggz0d85hkq", "doc_id": 227964666, "children": []}, {"title": "恒科码牌音箱怎么充值流量", "url": "zf8z8nuw10v23326", "doc_id": 227582958, "children": []}]}, {"title": "联迪刷蓝云喇叭", "url": "qywtzr605q48vql9", "doc_id": 226477243, "children": [{"title": "联迪刷蓝云喇叭怎么播报上一笔订单", "url": "psasfgdnzkye03hg", "doc_id": 228060770, "children": []}, {"title": "联迪刷蓝云喇叭充满电可以用多久", "url": "grc5s8nbbglyyryw", "doc_id": 227917622, "children": []}, {"title": "联迪刷蓝云喇叭常见QA", "url": "", "doc_id": "", "children": [{"title": "刷蓝云喇叭开不了机，充电也开不了", "url": "gsr5dkgzflo8s9yh", "doc_id": 227551594, "children": []}]}, {"title": "联迪刷蓝云喇叭操作流程图（连接WIFI步骤）", "url": "zqq763i1e5omg5lv", "doc_id": 226479702, "children": []}, {"title": "联迪刷蓝云喇叭流量怎么充值", "url": "lwws8ool23m028g4", "doc_id": 226479068, "children": []}, {"title": "联迪刷蓝云喇叭绑定流程", "url": "kcwkprbn29ct5kil", "doc_id": 226478022, "children": []}]}, {"title": "WIFI云音箱", "url": "dk6t7tn0h2pb2npl", "doc_id": 226471208, "children": [{"title": "WIFI云音箱问题排查", "url": "dfrzkxgwgf4mwdg0", "doc_id": 226473187, "children": []}, {"title": "WIFI云音箱配网教程", "url": "xcu2o4bwcneww34t", "doc_id": 226472734, "children": []}, {"title": "WIFI云音箱绑定流程", "url": "nyyfdib9qkenuq59", "doc_id": 226471251, "children": []}]}, {"title": "博实结4G码牌音箱（含工牌音箱）", "url": "ggxqssd9wl8nen8q", "doc_id": 225749758, "children": [{"title": "4G码牌音箱产品优势", "url": "wcliwvymqktftbdg", "doc_id": 227273334, "children": []}, {"title": "4G码牌音箱按键介绍，怎么查看上一笔交易信息", "url": "kwkzmddp39hsb3a1", "doc_id": 225752055, "children": []}, {"title": "4G码牌音箱销卡了怎么处理", "url": "lvghs12vwgldz30f", "doc_id": 225752032, "children": []}, {"title": "4G码牌音箱绑定流程", "url": "mfabmr02h79sitpg", "doc_id": 225751711, "children": []}, {"title": "4G码牌音箱怎么充值流量", "url": "ik2fv4ywk8akm520", "doc_id": 225751644, "children": []}, {"title": "4G码牌音箱怎么调音量", "url": "lzx60zdl13y79goo", "doc_id": 225750043, "children": []}, {"title": "4G码牌音箱可以播报会员支付吗", "url": "ayc1100wvs6x40l5", "doc_id": 225749851, "children": []}]}]}, {"title": "硬件常见报错QA", "url": "suebw8zvxsidf451", "doc_id": 225577777, "children": [{"title": "Q：机器激活签到显示97交易失败", "url": "xhq1yc94vr5pzp8w", "doc_id": 226284382, "children": []}, {"title": "Q：机器刷卡报“X3-终端未登记请联系客服”", "url": "qgafhbixopavi3tg", "doc_id": 226283006, "children": []}, {"title": "Q：预授权完成撤销后，再次预授权完成，发现打印金额不对", "url": "tyne7g4b6ypyh8g1", "doc_id": 226282391, "children": []}, {"title": "Q：机器流量卡插上没有用、各种没网络情况", "url": "ggxwuzw72agvydgf", "doc_id": 226271859, "children": []}, {"title": "Q：机器开了门店同步、没有打印推送小票", "url": "xk1exozherchmpf8", "doc_id": 226271117, "children": []}, {"title": "Q：机器签到失败：未知错误，错误码B0", "url": "zgeu4ddz9q0ebngy", "doc_id": 226269187, "children": []}, {"title": "Q：机器签到提示B0交易失败", "url": "gads52hrbvle8iy2", "doc_id": 226168596, "children": []}, {"title": "Q：机器银行卡小票商户名称为“测试测试”", "url": "moobso8fyyt97f9l", "doc_id": 226167195, "children": []}, {"title": "Q：机器挥卡交易直接报“55密码错”", "url": "nyp5mmgvettyiz1s", "doc_id": 226135218, "children": []}, {"title": "Q：机器激活报 终端已激活（B1）", "url": "mokcwy242803m0ma", "doc_id": 226133326, "children": []}, {"title": "Q：刷卡交易错误码S1", "url": "dbgfsynzvh9msw21", "doc_id": 226131406, "children": []}, {"title": "Q：刷卡交易错误码L3", "url": "hkidu1ybr1hwo7mb", "doc_id": 226130175, "children": []}, {"title": "Q：刷卡交易错误码L2", "url": "btb6i5nc45vwkkzl", "doc_id": 226128230, "children": []}, {"title": "Q：刷卡交易报“MC-请求参数格式错误，必填参数为空”", "url": "mg583boifdnv8x6u", "doc_id": 226126072, "children": []}, {"title": "Q：刷卡交易错误码E4", "url": "htvcoynb1kxnnkyu", "doc_id": 226122903, "children": []}, {"title": "Q：刷卡交易错误码E3", "url": "qfdk1myauvs4n34o", "doc_id": 226121151, "children": []}, {"title": "Q：刷卡交易错误码E2", "url": "naam9hh772b62zaw", "doc_id": 226121130, "children": []}, {"title": "Q：刷卡交易错误码E1", "url": "oee4ett16s92ry7s", "doc_id": 226120361, "children": []}, {"title": "Q：刷卡交易报错返回“无效金额”", "url": "aop8ymlchb0r0z0a", "doc_id": 226119395, "children": []}, {"title": "Q：刷卡交易失败提示\"61\"", "url": "bl4tkvok5szs5gtl", "doc_id": 226118818, "children": []}, {"title": "Q：提示“纯磁条卡只限刷卡交易”", "url": "mevycgivf2qnkimr", "doc_id": 226117119, "children": []}, {"title": "Q：刷卡提示“该卡不支持刷卡，请插卡或挥卡”，或“请使用芯片”", "url": "oigei2hq86k2l3aw", "doc_id": 226116349, "children": []}, {"title": "Q：交易提示 \"安全处理失败(A7)\n或超出金额限制(61)", "url": "yges63nlg6wv7ve2", "doc_id": 226115841, "children": []}, {"title": "Q：激活时提示\"终端号未登记(97)\"或B1交易失败或\n联动设备报“终端号未登记，请联系代理商（97）”\n1、终端号未登记\n2、B1交易失败\n3、终端号未登记，请联系代理商", "url": "otzxiv4iik8hpcag", "doc_id": 226112458, "children": []}, {"title": "Q：绑定提示“超出机具绑定商户数量上限”", "url": "md03c5r7rwh1u8qg", "doc_id": 226103291, "children": []}, {"title": "Q：门店结算信息已更改，请重新绑定设备", "url": "yh59dwd75zpe66td", "doc_id": 225581469, "children": []}, {"title": "Q：报错：绑定失败，您无权操作此设备", "url": "ad2d60la1g03witz", "doc_id": 225618951, "children": []}, {"title": "Q：去绑定设备提示“商户刷卡权限未开通，请联系在线客服开通权限”", "url": "efaprwhfg7i0gdk1", "doc_id": 225579804, "children": []}, {"title": "Q：该门店已绑定门店", "url": "fh8g047w3da9wyn8", "doc_id": 225577892, "children": []}]}, {"title": "收款王3代", "url": "", "doc_id": "", "children": [{"title": "收款王3代功能QA", "url": "mhd8n8ckloaselgg", "doc_id": 226449642, "children": [{"title": "收款王3代如何恢复出厂设置", "url": "vt2pktiux2n82rrl", "doc_id": 227318067, "children": []}, {"title": "收款王3代如何修改密码", "url": "dot30l6gy259i7ct", "doc_id": 227315412, "children": []}, {"title": "收款王3代如何开启交接班并使用", "url": "kgayw01u0cslk9ed", "doc_id": 226465435, "children": []}, {"title": "收款王3代功能按键", "url": "qbmk4h58xuymp3xg", "doc_id": 226464668, "children": []}, {"title": "收款王3代小票打印模板", "url": "izvnmkkenwg3q16i", "doc_id": 226464205, "children": []}, {"title": "收款王3代银行卡收款开通流程", "url": "mvlgggtpo8x4c940", "doc_id": 226463051, "children": []}, {"title": "收款王3代可以播报收款语音吗", "url": "pq72zxqbs21ik907", "doc_id": 226462663, "children": []}, {"title": "收款王3代支持什么网络，如何连接", "url": "mttoknmefglfrvgr", "doc_id": 226454012, "children": []}, {"title": "收款王3代硬件参数介绍图", "url": "lc7909gouhwtrasl", "doc_id": 226453067, "children": []}, {"title": "收款王3代操作功能介绍图", "url": "ptlnldftiqh2stp8", "doc_id": 226451591, "children": []}, {"title": "收款王3代退款流程", "url": "nhgy78uh41fcz15z", "doc_id": 226450686, "children": []}]}, {"title": "收款王3代报错QA", "url": "tw9n3x1qz7111meo", "doc_id": 226100887, "children": [{"title": "收款王3代退款显示“96拒绝，交换中心异常，\n请稍后重试”", "url": "ub8fricuazme17vf", "doc_id": 229418473, "children": []}, {"title": "收款王3代收款显示“9996订单信息不存在”", "url": "eoznv83fh51udrte", "doc_id": 228454674, "children": []}, {"title": "收款王3代换绑后银行卡商户名称没有变更", "url": "fpe58s9fmqmyhgqp", "doc_id": 226284828, "children": []}, {"title": "收款王3代刷卡报97经纬度信息未上送", "url": "ri7t5vi7358xl55u", "doc_id": 226278776, "children": []}, {"title": "收款王3代刷卡报错MAC鉴别失败(A0)” \n或者 “安全处理失败(B2)", "url": "pu694x3pwxmc4akx", "doc_id": 226113050, "children": []}, {"title": "收款王3代绑定报“机构设备信息为空”", "url": "cg77z4izk842kfc7", "doc_id": 226103805, "children": []}, {"title": "收款王3代设备绑定提示“绑定失败，请重试”", "url": "dkfis73qel7v4nvq", "doc_id": 226100996, "children": []}]}, {"title": "收款王3代绑定流程", "url": "dir0fo9lobpq1d2m", "doc_id": 226449671, "children": []}]}, {"title": "收款王4代", "url": "pxkogsgwfdtm6e9d", "doc_id": 226291063, "children": [{"title": "收款王4代功能QA", "url": "waicy2g9eq22lwd0", "doc_id": 226319244, "children": [{"title": "收款王4代查看订单“报文过长,格式错误”", "url": "bgsb30uz8lm9g2em", "doc_id": 228462982, "children": []}, {"title": "收款王4代，怎么关闭退款功能", "url": "xey8kyiea0kkobd8", "doc_id": 228450113, "children": []}, {"title": "收款王4代如何开启交接班并使用", "url": "lkk9fuoxsozuo6hg", "doc_id": 226467851, "children": []}, {"title": "收款王4代怎么定额收款", "url": "qlc5rmffr8okynye", "doc_id": 226439851, "children": []}, {"title": "收款王4代如何修改密码", "url": "guceyf4rtu90r27x", "doc_id": 226326428, "children": []}, {"title": "收款王4代音量如何设置", "url": "bhncdaka5f1fl4eu", "doc_id": 226326252, "children": []}, {"title": "收款王4代怎么退款", "url": "bgkirswnfnofbeu6", "doc_id": 226325992, "children": []}, {"title": "收款王4代流量卡怎么安装", "url": "orx2i6n7tel1cvcg", "doc_id": 226325244, "children": []}, {"title": "收款王4代支持什么网络，如何连接", "url": "ghgkwfc5lpufhkgs", "doc_id": 226324024, "children": []}, {"title": "收款王4代怎么恢复出厂设置", "url": "yxal2uxonclgrg5i", "doc_id": 226321940, "children": []}, {"title": "收款王4代怎么设置息屏时间", "url": "rafdlgknmeee4mwc", "doc_id": 226320431, "children": []}]}, {"title": "收款王4代报错QA", "url": "gf6qzflbhvg73cwh", "doc_id": 226291116, "children": [{"title": "收款王4代收款显示“[9999]网络繁忙，请稍\n后再试”", "url": "gvad5bmuzxfq1gxn", "doc_id": 229256284, "children": []}, {"title": "收款王4代显示“同步服务器地址”", "url": "ugdl3a6724btfgw4", "doc_id": 227620190, "children": []}, {"title": "收款王4代报错“无应用程序(-307)”", "url": "il71nxone0b6amxe", "doc_id": 227594424, "children": []}, {"title": "收款王4代报错“返回数据异常”", "url": "wgkzg0vfkkexrz8z", "doc_id": 227293468, "children": []}, {"title": "收款王4代交易结果查询超时，请与顾客确认交易结果", "url": "hqngn1n1bmyw8g57", "doc_id": 226292818, "children": []}, {"title": "收款王4代扫码显示初始化扫码失败-3402", "url": "shbg6kgaix9nu9bg", "doc_id": 226291345, "children": []}]}, {"title": "收款王4代绑定流程", "url": "qig7qbupi6qc2qgq", "doc_id": 226319835, "children": []}]}, {"title": "联迪A8", "url": "", "doc_id": "", "children": [{"title": "A8功能QA", "url": "", "doc_id": "", "children": [{"title": "A8怎么设置息屏时间", "url": "bocyruf8dsw2n31g", "doc_id": 230064722, "children": []}, {"title": "A8怎么激活刷卡功能", "url": "mq1pad2nxg2mvo34", "doc_id": 228603950, "children": []}, {"title": "A8怎么生成收款二维码让顾客扫码支付吗", "url": "bali14hd8zqsmasb", "doc_id": 227588647, "children": []}, {"title": "A8的付呗智能POS应用可以会员支付吗", "url": "bmihdmmeadtofng2", "doc_id": 227588217, "children": []}, {"title": "A8怎么修改密码", "url": "nfsesuq5mfkgtgir", "doc_id": 227316681, "children": []}, {"title": "A8易生APP操作员账号密码是多少", "url": "xrsgewehz3t84565", "doc_id": 227121706, "children": []}, {"title": "A8按键介绍", "url": "ds3m04iz42ln913z", "doc_id": 226316768, "children": []}, {"title": "A8流量使用太快", "url": "apen3kw7lehqbmwg", "doc_id": 226277340, "children": []}, {"title": "A8小票规格", "url": "fre7v1kkhalytgxe", "doc_id": 225745823, "children": []}, {"title": "A8怎么核销抖音劵", "url": "lqz1efyxzzr69e35", "doc_id": 225745548, "children": []}, {"title": "A8怎么打印和播报其他通道收款的订单", "url": "xipwsxpmeuyit8d6", "doc_id": 225745093, "children": []}, {"title": "A8符号图标介绍", "url": "pmp5gqd5gtmgpcin", "doc_id": 225743483, "children": []}, {"title": "A8打印浓度设置", "url": "tpapgb223q77wcgx", "doc_id": 225743075, "children": []}, {"title": "A8如何打印测试", "url": "xdq0s7wm4souooet", "doc_id": 225742983, "children": []}, {"title": "A8如何恢复出厂设置", "url": "rb3bgngs6691on32", "doc_id": 225737749, "children": []}, {"title": "A8支持什么网络", "url": "gdrbqbwkmy76c2bl", "doc_id": 225737397, "children": []}, {"title": "配置对账周期", "url": "vvsrrhgu9xg0vgdz", "doc_id": 229211112, "children": []}]}, {"title": "A8报错QA", "url": "", "doc_id": "", "children": [{"title": "A8绑定显示“[9996]终端序列号校验未通过，不支持开通POS终端”", "url": "gxy0yqgnoo1qwnhu", "doc_id": 229414774, "children": []}, {"title": "A8激活显示“终端激活失败 终端已激活(B1)”", "url": "zq85lygeq6g6gke3", "doc_id": 228577874, "children": []}, {"title": "A8收款显示“错误代码97，终端未登记”", "url": "oznynkb60k1005bu", "doc_id": 228457766, "children": []}, {"title": "A8收款显示“交易路由-获取通道商户失败-L”", "url": "nusai070t2t9k0yl", "doc_id": 228456284, "children": []}, {"title": "A8易生绑定显示“[9996]终端机具号不可用,请联系运营查看NC系统”", "url": "iq16lhn7b5340zua", "doc_id": 227619578, "children": []}, {"title": "A8签到显示“SDK调用异常，请到应用商店找到【乐刷收银通]安装后，再重试”", "url": "snp98hakpxtlgumf", "doc_id": 227466943, "children": []}, {"title": "A8绑定提示“连接失败，请检查通讯参数”", "url": "loalc14h1e1rwyym", "doc_id": 226102459, "children": []}, {"title": "A8绑定提示“无权限查看该设备sn绑定情况”", "url": "xv7ypw0ss64inw36", "doc_id": 226100130, "children": []}, {"title": "A8绑定提示“绑定失败，请重试”", "url": "px1ritgwpy8fua3e", "doc_id": 226098454, "children": []}, {"title": "A8绑定提示“设备sn信息不存在”", "url": "gx0wgk3kd1goy5dn", "doc_id": 226097554, "children": []}, {"title": "A8绑定提示“设备和商户所属通道不匹配”", "url": "ggtf15zkpc3fz2gx", "doc_id": 226097038, "children": []}, {"title": "A8绑定提示“设备sn未关联乐刷sn”", "url": "ss5eh45d6mnbw6gl", "doc_id": 226096519, "children": []}, {"title": "A8绑定提示“商户尚未报备银联，未生成银联商编”", "url": "meokt63wvkg7vn55", "doc_id": 226095937, "children": []}, {"title": "A8提示不支持的交易L1/L1交易失败/错误码L1\n/请检查通道商户信息L1", "url": "kwgyl3gvwhe9s77e", "doc_id": 226094041, "children": []}, {"title": "A8银行预授权撤销失败", "url": "khbkgu<PERSON>udavgt", "doc_id": 226093276, "children": []}, {"title": "A8刷卡提示无法使用金融设备", "url": "ey3n7cc5fm6xzvg7", "doc_id": 226092405, "children": []}, {"title": "A8签到提示“签到失败failed  to connect......”", "url": "bart2blrpki1gk9g", "doc_id": 226090344, "children": []}, {"title": "A8刷卡提示“disk I/O error (code 4874): , while compiling: PRAGMA journal_mode^apiName=SwipeCardTrans”", "url": "mgsmarnws7sg9155", "doc_id": 226087954, "children": []}, {"title": "A8绑定显示“[9996]终端安装地址长度不超过50", "url": "upkw9tq40y19zaq1", "doc_id": 225958498, "children": []}, {"title": "A8激活时跳转到未知页面", "url": "qdfxqcz401iy05aw", "doc_id": 225955979, "children": []}, {"title": "A8交易时报“发卡方不允许该卡在本终端进行此交易”", "url": "gwsr7v48lmxqwf44", "doc_id": 225954535, "children": []}, {"title": "A8刷卡显示“该商户未开启银行卡支付”", "url": "yveu452g3uk549et", "doc_id": 225953388, "children": []}, {"title": "A8收款显示交易失败：错误代码：06", "url": "hf796f72gnykf3ou", "doc_id": 225951612, "children": []}, {"title": "A8刷卡激活显示“SDK调用异常，请重试”", "url": "eg6h0pz8r3t2dezp", "doc_id": 225950372, "children": []}, {"title": "A8显示无法使用金融设备", "url": "xq1ag0whhlfrllas", "doc_id": 225744356, "children": []}, {"title": "A8显示设备被锁定", "url": "zsba4s3fcezusgts", "doc_id": 225743929, "children": []}, {"title": "易生通道A8设备绑定商户出现“绑定失败，易生通道下的机具需要先解绑”", "url": "zllab79ezz6fc4gx", "doc_id": 225599346, "children": []}, {"title": "易生通道A8设备A门店更换B门店", "url": "omp7xxvkqux9dc0c", "doc_id": 225598564, "children": []}, {"title": "易生通道A8激活报：“机构设备待审核”", "url": "xbb83ypbgcmgq2nr", "doc_id": 225598026, "children": []}, {"title": "乐刷通道A8下载三方程序提示“终端未登记”", "url": "xvaw1l1c9l3hn43g", "doc_id": 225596499, "children": []}, {"title": "乐刷通道A8获取的订单缺失授权码", "url": "ex3g6654mo7vgh29", "doc_id": 225584735, "children": []}]}, {"title": "A8换绑流程", "url": "oz8yovpkmmv7b7hb", "doc_id": 225584608, "children": []}, {"title": "A8解绑流程", "url": "hfagl45mfmc0lysy", "doc_id": 225584522, "children": []}, {"title": "A8绑定流程", "url": "rlmmrhqtdkn9lttb", "doc_id": 225578863, "children": []}]}, {"title": "联迪E350P", "url": "", "doc_id": "", "children": [{"title": "E350P常见QA", "url": "", "doc_id": "", "children": [{"title": "联迪E350P设备打印小票信息没有显示备注信息", "url": "mnwvmavy22k3p4vf", "doc_id": 228452426, "children": []}, {"title": "联迪E350P开机黑屏，显示“Attack detected\nCode 5013”", "url": "vxiyivkg91e6x5n0", "doc_id": 227625377, "children": []}, {"title": "联迪E350P开机到不了首页", "url": "yzbrhyfoaf7uv78b", "doc_id": 227624822, "children": []}, {"title": "联迪E350P如何修改密码", "url": "qcpvz7a3cl7o40by", "doc_id": 227317719, "children": []}, {"title": "联迪E350P开机没有付呗应用", "url": "pbqgilmzv1vbdwl4", "doc_id": 227282563, "children": []}, {"title": "联迪E350P刷卡显示作弊嫌疑", "url": "exg0599yydygv3g1", "doc_id": 227281499, "children": []}, {"title": "联迪E350P显示不支持扫码", "url": "grb01pag101ztifh", "doc_id": 227278679, "children": []}, {"title": "联迪E350P恢复出厂设置", "url": "wwzs9qcmnivaqt45", "doc_id": 227156815, "children": []}, {"title": "联迪E350P账单筛选汇总点击打印明细小票没反应", "url": "oh9u0qdkcqt2zg5i", "doc_id": 226292052, "children": []}, {"title": "联迪E350P交易或升级设备报“解析报文失败”", "url": "rzqmk855gikoh4b5", "doc_id": 226283926, "children": []}, {"title": "联迪E350P播报来源打印来源设置列表闪退", "url": "rduqq8u66ogh2682", "doc_id": 226280159, "children": []}, {"title": "联迪E350P更新失败，报错“固件校验出错，UNS报校验错误-4908”", "url": "aox3b1h9g1qgtqgn", "doc_id": 226279780, "children": []}, {"title": "联迪E350P程序包升级失败", "url": "lgm55femdso7zmt4", "doc_id": 226276629, "children": []}, {"title": "联迪E350P刷卡报作弊", "url": "rvaqrxcqmqrm5lmw", "doc_id": 226275571, "children": []}, {"title": "联迪E350P签退时账单数还有上次的", "url": "gg51cg7n0viwxgmo", "doc_id": 226273065, "children": []}, {"title": "联迪E350P报错PINPAD故障\n打开PINPAD失败-2835", "url": "zkx6bqabc0o5fqa1", "doc_id": 226164259, "children": []}, {"title": "联迪E350P支持哪些通道", "url": "gq28p3untcibhk8k", "doc_id": 225638568, "children": []}, {"title": "联迪E350P支持哪些网络", "url": "elpow2ccdxtickek", "doc_id": 225637732, "children": []}]}, {"title": "联迪E350P换绑流程", "url": "qrryg2ggc247l31g", "doc_id": 225629584, "children": []}, {"title": "联迪E350P解绑流程", "url": "tatyf8kwxkaqlpb4", "doc_id": 225628301, "children": []}, {"title": "联迪E350P绑定流程", "url": "pi5drcdw5g3cdzf5", "doc_id": 225628109, "children": []}]}, {"title": "新大陆T9云POS", "url": "", "doc_id": "", "children": [{"title": "T9常见功能QA", "url": "", "doc_id": "", "children": [{"title": "T9绑定了商户，用一段时间就会自动解绑，显示未绑定状态，又需要重新绑定", "url": "sfw60r1yryl85sq2", "doc_id": 228458304, "children": []}, {"title": "T9如何修改密码", "url": "ypc2o3gaqutfb9tx", "doc_id": 227317844, "children": []}, {"title": "T9打印显示打印机过热无法打印", "url": "pnfy4og11nx5gccy", "doc_id": 226645094, "children": []}, {"title": "T9支持哪些网络", "url": "lyqo5vnib5s002x2", "doc_id": 226636240, "children": []}, {"title": "T9如何恢复出厂设置", "url": "zgk8kp15stvxa52m", "doc_id": 225744913, "children": []}]}, {"title": "T9常见报错QA", "url": "", "doc_id": "", "children": [{"title": "T9刷卡显示“设备签到失败[pin密钥安装失败\n-1,请检查”", "url": "ooxnm69bfxx7s566", "doc_id": 229571380, "children": []}, {"title": "T9显示“商户状态异常,暂不能收款\n请联系业务员处理”", "url": "ga6wtmka23f5ro11", "doc_id": 229420021, "children": []}, {"title": "T9富友绑定显示“绑定失败 网络繁忙 请稍后再试”", "url": "evlswagt7zs3hzxf", "doc_id": 229259626, "children": []}, {"title": "T9激活显示“激活设备未列入白名单”", "url": "mm6oc32gmd4qw6bg", "doc_id": 228463750, "children": []}, {"title": "T9激活显示“激活次数超限，不允许激活”", "url": "oteieepqmsy7y6wd", "doc_id": 228456806, "children": []}, {"title": "T9开机到不了主页", "url": "izarnra6bdc17spk", "doc_id": 227624006, "children": []}, {"title": "T9显示“安全警告，安全触发”", "url": "fca3kd7ewlfyz6px", "doc_id": 227623431, "children": []}, {"title": "T9打印不了小票或者乱码", "url": "zhzch0gekxbhk1w4", "doc_id": 227622060, "children": []}, {"title": "T9显示“同步账单失败，接收失败请重试”", "url": "le5ydi8enqwd38td", "doc_id": 227618742, "children": []}, {"title": "T9收款显示“支付失败 银联风险受限[4038800]”", "url": "vzlsog5oel2z04r3", "doc_id": 227596886, "children": []}, {"title": "T9首页突然看不到预授权了，以前是有的", "url": "ytr11zi9xbd948y0", "doc_id": 227301863, "children": []}, {"title": "T9交易时报“0007-交易中风控校验失败”", "url": "nrn6x9iippgmyatt", "doc_id": 226281578, "children": []}, {"title": "T9激活时报“机构设备待审核”", "url": "lo5dp2durs4ik1re", "doc_id": 226280687, "children": []}, {"title": "T9无法预授权部分结算", "url": "gn7arxu8uua0huig", "doc_id": 226274612, "children": []}, {"title": "T9绑定失败 连接失败 请检查通讯参数!", "url": "hkm4pwequ3d3or4i", "doc_id": 225617744, "children": []}]}, {"title": "新大陆T9云POS解绑流程", "url": "gicbo4bp3dpelerd", "doc_id": 226604794, "children": []}, {"title": "新大陆T9云POS换绑流程", "url": "ztrlp3zo85soo8z9", "doc_id": 226604783, "children": []}, {"title": "新大陆T9云POS绑定流程", "url": "vwbfe6bfg66no12o", "doc_id": 226604673, "children": []}]}, {"title": "商米POS", "url": "", "doc_id": "", "children": [{"title": "商米POS常见QA", "url": "", "doc_id": "", "children": [{"title": "商米POS如何修改密码", "url": "zch4rwf6s7w16pnv", "doc_id": 227317895, "children": []}, {"title": "商米POS没有刷卡按钮", "url": "bs9s130x3vgnbegt", "doc_id": 226270409, "children": []}, {"title": "商米POS支持哪些通道", "url": "mzx02xziexlktdi0", "doc_id": 225652478, "children": []}, {"title": "商米POS支持哪些网络", "url": "mihehbggcyogxben", "doc_id": 225649969, "children": []}]}, {"title": "商米POS绑定流程", "url": "ezbpoer1cbg7oh6q", "doc_id": 225631808, "children": []}, {"title": "商米POS解绑流程", "url": "abtfuettxo7nzyge", "doc_id": 225632063, "children": []}, {"title": "商米POS换绑流程", "url": "ypxn9hq70v3napxs", "doc_id": 225632398, "children": []}]}, {"title": "老款设备", "url": "yh93aa3m5wsxysgi", "doc_id": 226298562, "children": [{"title": "意锐增强版", "url": "li422qt9uio9t16h", "doc_id": 227148713, "children": [{"title": "意锐增强版绑定流程", "url": "gsvsun46xmv9bkv8", "doc_id": 227148738, "children": []}]}, {"title": "波普51", "url": "sm5ygpdng8kyatig", "doc_id": 227144207, "children": [{"title": "波普51功能介绍图", "url": "gcdtbhpa4pig16y7", "doc_id": 227147850, "children": []}, {"title": "波普51绑定流程", "url": "gni967sdsq1dudwc", "doc_id": 227144252, "children": []}]}, {"title": "自研点餐盒子（付呗扫码点餐）", "url": "ikdu79xmsa73g25m", "doc_id": 226612873, "children": [{"title": "自研点餐盒子FAQ", "url": "gdh8k4qn9tihrb3h", "doc_id": 226623993, "children": []}, {"title": "自研点餐盒子打印机设置", "url": "giwsowlq3fcu7gld", "doc_id": 226621765, "children": []}, {"title": "自研点餐盒子绑定流程", "url": "rpmynb0gid2zmlft", "doc_id": 226614315, "children": []}, {"title": "自研点餐盒子安装流程", "url": "xg5sln4lkmzok1qp", "doc_id": 226613490, "children": []}]}, {"title": "收款王2代", "url": "kykr6b775eiewgs7", "doc_id": 227284346, "children": [{"title": "收款王2代怎么生成二维码收款", "url": "kg16ab9a6kd0zb2e", "doc_id": 227312357, "children": []}, {"title": "收款王2代怎么固定金额收款", "url": "xx4z7s8pf0omlzks", "doc_id": 227312192, "children": []}, {"title": "收款王2代息屏时间如何设置", "url": "qsvp48p75fr6a1yh", "doc_id": 227311407, "children": []}, {"title": "收款王2代怎么恢复出厂设置", "url": "qmgm1o6npq6w3svw", "doc_id": 227310957, "children": []}, {"title": "收款王2代怎么修改密码", "url": "su2855mrgxbngneu", "doc_id": 227310436, "children": []}, {"title": "收款王2代键盘功能说明", "url": "bn7kgng92he6a4w3", "doc_id": 227309948, "children": []}, {"title": "收款王2代怎么调整音量", "url": "ww31b8f4ccw60ckf", "doc_id": 227305369, "children": []}, {"title": "收款王2代怎么开启交接班", "url": "hif5v5k1of9ta6w8", "doc_id": 227302481, "children": []}, {"title": "收款王2代怎么退款", "url": "hfs3g69p5gnyok0a", "doc_id": 227302033, "children": []}, {"title": "收款王2代支持什么网络", "url": "avas5xevdc48fb3g", "doc_id": 227286249, "children": []}, {"title": "收款王2代绑定流程", "url": "vpwk7gw2z4h1vbl8", "doc_id": 227284484, "children": []}]}, {"title": "收款王1代", "url": "pg8xlwkgsuobzawq", "doc_id": 226299364, "children": [{"title": "收款王1代怎么修改密码", "url": "ugnxzsr2ztfoldsh", "doc_id": 227315265, "children": []}, {"title": "收款王1代怎么生成二维码收款", "url": "hdh0hvy88f839bn2", "doc_id": 227314812, "children": []}, {"title": "收款王1代怎么固定金额收款", "url": "gqlh3xuarz27n799", "doc_id": 227314727, "children": []}, {"title": "收款王1代怎么开启交接班", "url": "iw7g4txsz5r51gkz", "doc_id": 227314630, "children": []}, {"title": "收款王1代怎么恢复出厂设置", "url": "bbi37ixt23dpmzq2", "doc_id": 227314295, "children": []}, {"title": "收款王1代绑定流程", "url": "gs7c9vi5u6hwadil", "doc_id": 226302265, "children": []}, {"title": "收款王1代怎么调整音量", "url": "yr092qhbeuczn4y9", "doc_id": 226314949, "children": []}, {"title": "收款王1代怎么操作退款", "url": "ohfbcp5xl4ozd1fe", "doc_id": 226314527, "children": []}, {"title": "收款王1代支持什么网络", "url": "yxsdurdwygrh6mi5", "doc_id": 226313142, "children": []}, {"title": "收款王1代按键音如何设置开启或关闭", "url": "qiegm9w1zgv8y5qo", "doc_id": 226312059, "children": []}, {"title": "收款王1代怎么调整时间", "url": "fhi4wo9znndu146f", "doc_id": 226304125, "children": []}, {"title": "收款王1代怎么切换字母或者符号", "url": "pp328ndpe318cbfp", "doc_id": 226303444, "children": []}, {"title": "收款王1代开机一直报错：获取最新版本“数据接收中”", "url": "cscif6txab82r5i6", "doc_id": 226300383, "children": []}, {"title": "收款王1代开机后设备一直“数据接收失败，请重试”", "url": "yzzxb7g2l8033q72", "doc_id": 226298637, "children": []}, {"title": "收款王1代收款显示“跨日后，该订单不可重复发起”", "url": "eg2wdm8v0irg1eww", "doc_id": 226299298, "children": []}]}]}, {"title": "碰一下", "url": "vhgh4sipzpgcnp0t", "doc_id": 224349375, "children": [{"title": "碰一下手持2.0（N7）", "url": "go1ms3ryl3hpkyqb", "doc_id": 226633967, "children": [{"title": "碰一下手持2.0开机显示设备联网中", "url": "fmawekgsr7gl3xmn", "doc_id": 229575469, "children": []}, {"title": "碰一下手持2.0收款显示“收款失败”", "url": "ho6l05sg4k2s9514", "doc_id": 229566313, "children": []}, {"title": "碰一下手持2.0显示系统故障", "url": "hf5m07gg9c59izmi", "doc_id": 228462581, "children": []}, {"title": "碰一下手持2.0怎么播报其他通道订单", "url": "pcre6xq2i7144xgp", "doc_id": 226636700, "children": []}, {"title": "碰一下手持2.0怎么查看账单及退款", "url": "izqupds4lond33lr", "doc_id": 226636472, "children": []}, {"title": "碰一下手持2.0怎么定额收款", "url": "fi7llkv783t8a89i", "doc_id": 226635513, "children": []}, {"title": "碰一下手持2.0支持什么网络，怎么连接", "url": "dg5nmptvtgahau5i", "doc_id": 226634777, "children": []}, {"title": "碰一下手持2.0绑定流程", "url": "tsmg1cng2dgexm96", "doc_id": 226633995, "children": []}]}, {"title": "碰一下手持1.0（N5）", "url": "iwes5v2odq04snsa", "doc_id": 226633891, "children": [{"title": "碰一下手持1.0首页没有付呗应用，只显示支付演示试用", "url": "ke3hmozsilpa864z", "doc_id": 228455446, "children": []}, {"title": "碰一下手持1.0修改密码", "url": "igczle9guin9ktqh", "doc_id": 227979739, "children": []}, {"title": "碰一下手持1.0怎么推送付呗应用", "url": "dabu78rmkx5acql9", "doc_id": 227912155, "children": []}, {"title": "碰一下手持1.0绑定流程", "url": "rypgh4em7z1t0erg", "doc_id": 226631126, "children": []}, {"title": "碰一下手持1.0退款流程", "url": "aog272mv2b6y25w8", "doc_id": 226632530, "children": []}, {"title": "碰一下手持1.0收款怎么打印小票", "url": "eg34zpb4qfq6i39i", "doc_id": 226632953, "children": []}, {"title": "碰一下手持1.0功能介绍", "url": "vi1iceecof298xuy", "doc_id": 226633832, "children": []}, {"title": "碰一下报错QA", "url": "nnuv5w8ngudml063", "doc_id": 225966279, "children": [{"title": "Q：迈进方案未完成，商家确认中，请等待商家在手机上完成", "url": "anxl1q6vsd84gga7", "doc_id": 225966744, "children": []}, {"title": "Q：设备:LDN7HDQM024100922456已下单，\n请检查", "url": "hubf769kx2q3oews", "doc_id": 225966337, "children": []}]}]}, {"title": "区代支付宝小二碰一下", "url": "ez1ynxkwghrm4ldx", "doc_id": 225321360, "children": []}, {"title": "手持碰一下n7设备功能", "url": "lo73f3q7u6wlg455", "doc_id": 224361754, "children": []}, {"title": "碰一下流程类FAQ", "url": "pz6ml6o7bgpqt941", "doc_id": 224349530, "children": []}]}]}, {"title": "收款额度", "url": "", "doc_id": "", "children": [{"title": "乐刷通道额度表", "url": "thgg9lyw2ldx7go0", "doc_id": 226171305, "children": []}, {"title": "随行付通道额度表", "url": "ryso8hgb86948b3r", "doc_id": 226171625, "children": []}, {"title": "易生通道额度表", "url": "dzc010ewqh2882sn", "doc_id": 226174589, "children": []}, {"title": "富友通道额度表", "url": "uv2ocp54qi0h6361", "doc_id": 226174987, "children": []}, {"title": "拉卡拉交易额度表", "url": "micl8g4rs95qsfui", "doc_id": 226174187, "children": []}, {"title": "国通星驿通道额度表", "url": "qfqbxcba07zo7nui", "doc_id": 226175153, "children": []}, {"title": "威富通通道额度表", "url": "em7ph7a6rh9g4zoy", "doc_id": 226175042, "children": []}, {"title": "银盛通道额度表", "url": "zz8x6icqrfw81ctl", "doc_id": 226171198, "children": []}, {"title": "收款额度常见QA", "url": "vyrrlrabs0hn6wmx", "doc_id": 227913626, "children": []}]}, {"title": "常见报错QA", "url": "", "doc_id": "", "children": [{"title": "进件驳回QA", "url": "", "doc_id": "", "children": [{"title": "乐刷-常见报错QA", "url": "mxxqb4dg6giiy9km", "doc_id": 220446185, "children": [{"title": "Q：企业:结算确认书(盖公章)须法人手持拍摄且内容清晰;", "url": "kye7vs0gkrv9hbvg", "doc_id": 229043892, "children": []}, {"title": "Q：1.商户简称描述的行业/三合照体现的经营内容与营业执照行业不符", "url": "nm0bg4d47yhkk7lm", "doc_id": 229014716, "children": []}, {"title": "Q：超出代理区域，请重新选择所在省市", "url": "vs42ehnosx1rgci9", "doc_id": 227925724, "children": []}, {"title": "Q：扫码费率(D1结算)支付宝费率设置区间\n3.00~10.00", "url": "qxdw0sn7qwt9552s", "doc_id": 227398268, "children": []}, {"title": "Q：注册信息不一致，请修改[6100]", "url": "qwhsnrdbu1boelzu", "doc_id": 227263201, "children": []}, {"title": "Q：机具布放地址详细地址不符合规范", "url": "pc0xpud496gz0xoc", "doc_id": 227260184, "children": []}, {"title": "Q：商户经营地址经纬度不能为空", "url": "natgigo8negs0k70", "doc_id": 227084335, "children": []}, {"title": "Q：商户简称命中敏感词，禁止入网", "url": "ga1g4estggsnfk03", "doc_id": 227082236, "children": []}, {"title": "Q：根据我司风险监测系统的监测结果，该账户可能存在风险，暂时不能创建。详情可咨询客服”", "url": "qtwyx9g19nkryr1p", "doc_id": 222431903, "children": []}, {"title": "Q：商户资料存在风险，不允许进件", "url": "nvnygwnflrtobsky", "doc_id": 222431849, "children": []}, {"title": "Q：1.不支持此类修改;", "url": "sxud2v3mm17u8bgv", "doc_id": 227064024, "children": []}, {"title": "Q：工商四要素身份证号比对不通过", "url": "rb2gan54n5t29srw", "doc_id": 226952745, "children": []}, {"title": "Q：商户信息修改失败-商户简称修改次数超限，请次月重试", "url": "bky2bgzc3urq6msz", "doc_id": 226279946, "children": []}, {"title": "Q：上报接口返回：商户信息修改失败 - 营业执照名称和商户简称不一致", "url": "ufamsdgus8nlenot", "doc_id": 225909678, "children": []}, {"title": "Q：工商鉴权返回经营状态异常，审核退回", "url": "hvxc6plggrtx525i", "doc_id": 225908057, "children": []}, {"title": "Q：商户信息修改失败-交易结算状态异常商户提交信息格式异常", "url": "gdf2gsbg410un7ir", "doc_id": 225576038, "children": []}, {"title": "Q：报错：审核成功;商户地址信息有误;地址信息有误", "url": "ln3xqpna4rf8gw34", "doc_id": 225574555, "children": []}, {"title": "Q:报错：商户类型选择错误", "url": "mcs0kbdzfb553pq7", "doc_id": 222505785, "children": []}, {"title": "Q:报错：[60201]000034调用乐刷接口异常 分店多次上报失败-此商户已存在上报成功，禁止多次上报", "url": "dzg50limr3pxikpe", "doc_id": 222505776, "children": []}, {"title": "Q:报错：商户经营行业不在受理范围内", "url": "lhqzm12qug17ys0r", "doc_id": 222505770, "children": []}, {"title": "Q:报错：彩票注册需要提供代销证相片/代销证法人姓名不一致", "url": "px2l4folwhzcid59", "doc_id": 222505754, "children": []}, {"title": "Q:报错：营业执照复印件需盖公章", "url": "cv2khi6el3ez44s3", "doc_id": 222504363, "children": []}, {"title": "Q:报错：统一社会信用代码填写错误", "url": "vxp513awyn4yang4", "doc_id": 222504354, "children": []}, {"title": "Q:报错：法人姓名与营业执照不一致", "url": "ux0nvo4x0kytn0gi", "doc_id": 222504341, "children": []}, {"title": "Q:报错：营业执照不允许截图/隔屏拍摄", "url": "vplooqhxs57z4mli", "doc_id": 222504329, "children": []}, {"title": "Q:报错：1.营业执照拍摄不清晰/不完整/不合规", "url": "ksg4dzq4zpkd6h6m", "doc_id": 222504320, "children": []}, {"title": "Q:报错：商户全称与营业执照名称不一致", "url": "sagxgogi0adw4a8r", "doc_id": 222504309, "children": []}, {"title": "Q:报错：1.商户简称不规范", "url": "skvh4a04gagzu5my", "doc_id": 222504302, "children": []}, {"title": "Q:报错：三要素鉴权失败，建议检查一下身份证号码，姓名，卡号是否有误", "url": "htdhh720msxpwpzk", "doc_id": 222504294, "children": []}, {"title": "Q:报错：银行卡相片的卡号不清晰，请写在纸上和银行卡一起拍照", "url": "wca9raw0c3ldflzn", "doc_id": 222504283, "children": []}, {"title": "Q:报错：银行卡相片无卡号，请拍摄有卡号的一面", "url": "hxnih02ebrga47cg", "doc_id": 222504275, "children": []}, {"title": "Q:报错：1.法人身份证正反面拍摄背景不一致，需重新拍摄上传", "url": "zebiaaah6iukv4zs", "doc_id": 222502838, "children": []}, {"title": "Q:报错：1.身份证拍摄不完整，需露四个角", "url": "bdemm4fhko5eg33u", "doc_id": 222502829, "children": []}, {"title": "Q:报错：身份证复印件需加盖公章", "url": "kderrhbets1f142g", "doc_id": 222502827, "children": []}, {"title": "Q:报错：手持身份证相片需与本人合照，身份证人像信息要清晰可见", "url": "tdgb503ph2oxkbp6", "doc_id": 222502823, "children": []}, {"title": "Q:报错：身份证即将过期/已过期", "url": "yc3gpx38o48304vn", "doc_id": 222502816, "children": []}, {"title": "Q:报错：1.银行卡照片截图/P图/隔屏，请现场重拍后上传", "url": "hiipfwaoeg27hlgx", "doc_id": 222502807, "children": []}, {"title": "Q:报错：请上传结算相片", "url": "pssi36gnpfthmut6", "doc_id": 222502799, "children": []}, {"title": "Q:报错：商户进件失败-不支持的卡类型，请提交借记卡或联系客服", "url": "gietri0lxxb78nf4", "doc_id": 222502795, "children": []}, {"title": "Q:报错：银行卡（账户）复印件相片需加盖公章", "url": "daycuc6snev81h9s", "doc_id": 222502789, "children": []}, {"title": "Q:报错：银行账号/结算账号填写的和照片不一致", "url": "mzunkzlpfccgl7x4", "doc_id": 222502775, "children": []}, {"title": "Q:报错：门头招牌内容与营业执照名称不匹配", "url": "psqlwar42k6w8hos", "doc_id": 222436302, "children": []}, {"title": "Q:报错：1.三合照与营业执照经营内容不符合，请提供实际经营照片", "url": "cfi59y0e9qez1rte", "doc_id": 222436295, "children": []}, {"title": "Q:报错：1.三合照不允许出现截图/隔屏拍摄", "url": "algwuwuzrl4g55zn", "doc_id": 222436288, "children": []}, {"title": "Q:报错：1.门头无招牌，建议法人手持营业执照与门头合照，营业执照内容需清晰可见", "url": "wugyf5a4kgydx9ho", "doc_id": 222436275, "children": []}, {"title": "Q:报错：1.营业执照拍摄不清晰/不完整/不合规", "url": "sz9rikox9in7fkqk", "doc_id": 222436264, "children": []}, {"title": "Q:报错：结算授权函拍摄不清晰", "url": "dgl6shdpdmpva5ds", "doc_id": 222436254, "children": []}, {"title": "Q:报错：结算授权函疑似P图/截图，请重新拍摄上传", "url": "rl3m789qevqgu9pm", "doc_id": 222436241, "children": []}, {"title": "Q:报错：结算确认书账号填写与结算卡相片不一致", "url": "rghhy2bmvsfqhk9t", "doc_id": 222436235, "children": []}, {"title": "Q:报错：结算授权函填写不合规", "url": "hlap12c76p4asrhw", "doc_id": 222436226, "children": []}, {"title": "Q:报错：1.身份证不可隔屏拍摄/截图/P图", "url": "srauqh7gk7un5da1", "doc_id": 222436212, "children": []}, {"title": "Q:报错：参数无效-mccCode不存在", "url": "hipvm8f44pz0m4is", "doc_id": 222434456, "children": []}, {"title": "Q:报错：参数无效-联行号不存在1", "url": "ygmz19pzz1z5bofz", "doc_id": 222434452, "children": []}, {"title": "Q:报错：商户进件失败-所用法人身份证注册次数已达上限", "url": "xbp5egoxoxq66384", "doc_id": 222434443, "children": []}, {"title": "Q:报错：Failed to invoke the method uploadImg in the service com.fshows.lifecircle.storagecore.facade.LeShuaApiFacade. Tried 1 time", "url": "hkedxss6z8an07tc", "doc_id": 222434357, "children": []}, {"title": "Q:报错：错误参数，param=certif_addr，msg=certif_addrmust not be blank", "url": "zm18tot8i3sn5f6d", "doc_id": 222434352, "children": []}, {"title": "Q:报错：1·审核成功；应用调用次数超限，包含调用频率超限：sub_code(isv.app-call-limited)", "url": "npsu37qyo2yuy8kr", "doc_id": 222434343, "children": []}, {"title": "Q:报错：工商信息鉴权失败：无数据[300000]", "url": "atnmrd3h04v1nd7p", "doc_id": 222434335, "children": []}, {"title": "Q:报错：门头照需要拍摄完整，招牌、内部经营环境需清晰可见", "url": "rum31wda3uxca3g7", "doc_id": 222434329, "children": []}, {"title": "Q:报错：1.门头照需体现招牌内容，不可出现手写拍摄", "url": "lmooxwrr0fya1xv6", "doc_id": 222434320, "children": []}, {"title": "Q:报错：三合照禁止重复使用", "url": "yoc818sm1d4e58p1", "doc_id": 222434306, "children": []}, {"title": "Q:报错：1.nul;商户存在风险(该商户历史存在风险记录，请提醒商户规范使用，如有异议，:请由服务商依据间连商户准入申诉指引代商户进行申诉)。详情:https://opendocs.alipay.com/p/015co2:sub code(CTU FAI", "url": "sa3dnkrg9xh1rouf", "doc_id": 222431907, "children": []}, {"title": "Q:报错：9996微信子商户号不存在", "url": "plqfbxvfyvtg17or", "doc_id": 222431894, "children": []}, {"title": "Q:报错：9996支付宝子商户号不存在", "url": "tglgptpu0khfmgom", "doc_id": 222431887, "children": []}, {"title": "Q:报错：商户进件失败-该商户存在风险，禁止入网", "url": "mo3uqsex6o4cc1b3", "doc_id": 222431870, "children": []}, {"title": "Q:报错：该手机号码注册商户过多，请输入真实的商户手机号", "url": "mpcay038dol1ma8u", "doc_id": 222431867, "children": []}, {"title": "Q:报错：调用乐刷接口异常分店多次上报失败-此商户已存在上报成功，禁止多次上报", "url": "iwu595vqcdqco9pa", "doc_id": 222431855, "children": []}, {"title": "Q:报错：商户进件失败-身份证在灰名单", "url": "xohl3imk9go1zcxk", "doc_id": 222431845, "children": []}, {"title": "Q:报错：签名校验错误", "url": "gf976n4f0e9rk0ep", "doc_id": 222431836, "children": []}, {"title": "Q:报错：审核驳回 [60200]乐刷进件请求未知异常", "url": "zrop8mqx18xrln1e", "doc_id": 222431784, "children": []}, {"title": "Q:报错：商户进件失败-非法人证件存在未结案工单，禁止入网，结算卡存在未结案工单，禁止入网", "url": "bxzmh7t3kc34q3c2", "doc_id": 222433134, "children": []}, {"title": "Q:报错：信息禁止修改-商户类型", "url": "ubpg2567zh68gva3", "doc_id": 222433125, "children": []}, {"title": "Q:报错：审核失败，理由：地址需详细到号或街道/省市区与备注项地区不一致", "url": "zgyrpmvsx415g4b4", "doc_id": *********, "children": []}, {"title": "Q:报错：银联侧处理异常，请发起查询", "url": "uvfuya9xt82capl2", "doc_id": *********, "children": []}, {"title": "Q:报错：结算人信息验证未成功，请核对结算人信息", "url": "wa2gy3m11eetee0e", "doc_id": *********, "children": []}, {"title": "Q:报错：业务请求过快，请稍后再试", "url": "pm49wpc1wwmromv6", "doc_id": *********, "children": []}, {"title": "Q:报错：参数无效-银行卡号格式末通过校验(account.bankCardNo)", "url": "wl3gcdhplgg06uop", "doc_id": *********, "children": []}, {"title": "Q:报错：必填参数为空-收银台照（shopinfo.cashierDeskPic）", "url": "va4re54s3uo419ag", "doc_id": *********, "children": []}, {"title": "Q:报错：审核失败-失败原因：参数无效-insidePic图片地址不正确", "url": "cb9wxwe4oa1og2w0", "doc_id": *********, "children": []}]}, {"title": "易生-常见报错QA", "url": "sifeo4sf80tvhgvq", "doc_id": *********, "children": [{"title": "Q：银行卡不显示[6100];", "url": "bocdze0253mi7zs8", "doc_id": *********, "children": []}, {"title": "Q：审核成功[0000]:x--该公司因违规，暂不能接入微信支付，可联系客服获取更多详情-FAIL-INVALID REQUEST;;", "url": "qiq769q7u9ti<PERSON><PERSON>", "doc_id": *********, "children": []}, {"title": "Q：注册地址不一致[6100};", "url": "iuxvue8qcov78f6g", "doc_id": *********, "children": []}, {"title": "Q：银联地区码错误[*********]", "url": "vxu04saqpm1ihhr1", "doc_id": 227409773, "children": []}, {"title": "Q：入网次数超限", "url": "br372im1cx84v5qk", "doc_id": 223256806, "children": []}, {"title": "Q：法人手机号运营商鉴权失败[不一致[01]]", "url": "ev4nbxiz5veluikw", "doc_id": 222430207, "children": []}, {"title": "Q：审核成功[0000];X-参数错误@address info.address-1001;", "url": "pwxrs46pdmytnrmi", "doc_id": 222430197, "children": []}, {"title": "Q：审核成功[0000];微信拆分错误:不允许开通的商户类型:7997[1031]", "url": "husau7zc9rqchv2p", "doc_id": 222430182, "children": []}, {"title": "Q：【无效参数】参数名：content，原因：must not be null", "url": "hb6gz9sxim4a8pqu", "doc_id": 222430171, "children": []}]}, {"title": "拉卡拉-常见报错QA", "url": "aywnlg5ykyfiy92g", "doc_id": 220778167, "children": [{"title": "Q：注册名称含有黑名单敏感词[青海狂飙歌数据服务有限公司]", "url": "hebdy9hwqopfsi0p", "doc_id": 227438619, "children": []}, {"title": "Q：对私结算账户必须借记卡校验不通过", "url": "du02rbnz47g38wz7", "doc_id": 227252381, "children": []}, {"title": "Q:报错：命中商户名称不支持变更", "url": "gqzg4k3xnat9lqg4", "doc_id": 222427286, "children": []}, {"title": "Q:报错：上传店铺经营照片与主营不符", "url": "hezlsngffoqo021g", "doc_id": 222399987, "children": []}]}, {"title": "泸州银行-常见报错QA", "url": "gp7k7wyzbv15r0ny", "doc_id": 225476004, "children": [{"title": "Q：[90302]账户验证失败:他行卡检查失败!;", "url": "povaumetiqoglnrg", "doc_id": 225476094, "children": []}]}, {"title": "随行付-常见报错QA", "url": "fqm0v0lg8i99mb0s", "doc_id": 220579001, "children": [{"title": "Q：商户信息变更表merchantlnfoModifyPic参数为空", "url": "hqo7tcw1kksywk2q", "doc_id": 227471092, "children": []}, {"title": "Q:报错：[50001]随行付图片上传异常", "url": "sik8b9b5m0n8gr21", "doc_id": 222431116, "children": []}, {"title": "Q:报错：传入经营类目与经营类型不匹配，请核实后重试（非乐刷商户进件教育类、高校食堂、生活服务-软件）", "url": "mg0fzluqbtnrczwx", "doc_id": 222431108, "children": []}, {"title": "Q:报错：商户信息变更表merchantInfoModifyPic参数为空", "url": "xgbiwfu667yn6opt", "doc_id": 222431103, "children": []}, {"title": "Q:报错：调用法人二要素鉴权服务未通过，请核实法人姓名、法人证件号是否填写有误", "url": "bx0ledk3bcg3d8fq", "doc_id": 222431100, "children": []}, {"title": "Q:报错：营业地址区县编码已停用，请修改后重试", "url": "zdsiwogfsnhtcuqm", "doc_id": 222431096, "children": []}, {"title": "Q:报错：商户类型不匹配，请核实后重试", "url": "en18ccus4r72hcgg", "doc_id": 222431094, "children": []}]}, {"title": "国通-常见报错QA", "url": "", "doc_id": "", "children": [{"title": "Q：营业执照认证结果:营业执照在工商网查不到记录;", "url": "yavx2nx0qklgpxgd", "doc_id": 229043026, "children": []}, {"title": "Q：营业执照经营者有误，请核实修改后重新提交:", "url": "miyxlb9f5i48cg78", "doc_id": 227451900, "children": []}]}, {"title": "银盛-常见报错QA", "url": "", "doc_id": "", "children": []}]}, {"title": "认证报错QA", "url": "km7mqmv1972ue671", "doc_id": 227806072, "children": [{"title": "支付宝认证QA", "url": "mbnyknd2vnvgscsd", "doc_id": 227806045, "children": [{"title": "Q：同一联系人只可关联一个小微商户，请更换联系人或商家主体类型", "url": "sy0cgwrbrt8s7ms9", "doc_id": 229772749, "children": []}, {"title": "Q：当前登录的支付宝账号(199******74)与提交申请的账号不一致。请使用账号(173******10)登录支付宝，再扫码确认。错误码:D009", "url": "pk4112cn2zqldktf", "doc_id": 229249248, "children": []}, {"title": "Q：当前认证商户的名称“马鞍山市雨山区康创食品经营部”和搜索的子商户号名称“马鞍山市雨山区康诺有品食品经营部”不一致，无法完成绑定。若仍需绑定此子商户号，可尝试联系服务商修改子商户名称。错误码:E001", "url": "bwigntvdogo8m157", "doc_id": 227806151, "children": []}, {"title": "Q：系统检测操作存在风险，绑定失败，请进入商家安全服务小程序解限", "url": "yy79npc3vvccgscy", "doc_id": 225612177, "children": []}]}, {"title": "微信认证QA", "url": "atgc20epxs08lrk0", "doc_id": 227387579, "children": [{"title": "Q：请填写18位的统一社会信用代码:", "url": "gfk5ete8unman20c", "doc_id": 229267375, "children": []}, {"title": "Q：你的身份认证已过期，请前往重新认证", "url": "zsig61ky2u7kzmas", "doc_id": 229254368, "children": []}, {"title": "Q：该商户涉嫌违法违规，暂时无法进行开户/修改信息操作", "url": "mu70decytuf51n5e", "doc_id": 229252332, "children": []}, {"title": "Q：门店门头照片不符合要求，请重新提交。要求照片不含微信支付商户不支持的经营场景。详细说明请参考Пhttps://kf.qq.com/faq/140225MveaUz150123raQRNN.html", "url": "doyl6vxn0gdtxgm6", "doc_id": 228394030, "children": []}, {"title": "Q：法人/经办人身份信息有误，请检查后重新上传", "url": "ynuugaqbhbys55kl", "doc_id": 227899263, "children": []}, {"title": "Q：该商户主体涉嫌跨类目经营，暂不支持绑定。请修改商户号的结算规则或处理相关违约单据后重新绑定", "url": "ga3cwwno1ikhm5uk", "doc_id": 227422134, "children": []}, {"title": "Q：你的商家资料异常，请重新进行注册", "url": "tqru10ctki13196e", "doc_id": 227405442, "children": []}, {"title": "Q：当前商家在该服务商下已确认1个商户号，达到上限", "url": "ork7wy8fr4t51f30", "doc_id": 227392806, "children": []}, {"title": "Q：该服务商下暂无该商户名称的商户号，请联系服务商确认。", "url": "ql0cvobrtnfwppa9", "doc_id": 227387674, "children": []}]}]}, {"title": "app使用报错QA", "url": "lklxg394k03fe1gs", "doc_id": 227392152, "children": [{"title": "付呗操作QA", "url": "pewbv7gcavb2ocfe", "doc_id": 227392250, "children": [{"title": "Q：发生了SSL错误，无法建立与该服务器的安全连接。", "url": "ggt8dxp1avzqep3c", "doc_id": 228399259, "children": []}, {"title": "Q：错读码:2003 错误信息:分享失败--[SA10001]权限验证失败，请检查你的签名以及该平台Appkev权限，解决方案:https://developer.umeng.com/docs166632/detail66787?um_channel=sd", "url": "hm7cuk68t3g8id1d", "doc_id": 227805456, "children": []}, {"title": "Q：未匹配到对应的角色信息", "url": "meiu8urxedmcbgya", "doc_id": 227420683, "children": []}, {"title": "Q：微信渠道配置信息不存在", "url": "dsooqebavqscuwwd", "doc_id": 227420554, "children": []}, {"title": "Q：未能找到使用指定主机名的服务器", "url": "ld1ri4acsuqs95n6", "doc_id": 227392335, "children": []}]}, {"title": "司南操作QA", "url": "ikt7wax0fk877q1p", "doc_id": 225595962, "children": [{"title": "Q：为更好保护您的权益，纯净模式增强防护下，仅支持安装经过华为应用市场安全检测的应用。了解纯净模式", "url": "ccan3uiydwtcomos", "doc_id": 228131591, "children": []}, {"title": "Q：登录司南报错 账号不存在", "url": "vo5f0n5god1la3mf", "doc_id": 226137066, "children": []}, {"title": "Q：司南登录报错：账号不存在或已禁用!", "url": "iebxk9zx78z8ny0z", "doc_id": 225596102, "children": []}, {"title": "Q：上级账号已到期，请联系上级处理", "url": "yz4x3pcpxmh1keh1", "doc_id": 225927768, "children": []}, {"title": "Q：开通受理商员工，提示“姓名、身份证号、手机号信息不一致，请重试", "url": "it0k66qkspa9i74r", "doc_id": 225450295, "children": []}, {"title": "Q：受理商开户，提示：当前因为风控原因不可添加商户", "url": "znlfehk97lb9hgpz", "doc_id": 225476534, "children": []}]}]}, {"title": "交易报错QA", "url": "uirw0xeupg0yygsg", "doc_id": 227387845, "children": [{"title": "微信交易报错QA", "url": "dlhdbtedwguzb1e1", "doc_id": 222512418, "children": [{"title": "Q：当前商户存在异常，暂时无法支付", "url": "kbeumeur9yy1pg2s", "doc_id": 229047958, "children": []}, {"title": "Q：该商家本月可向你收款最高500元交易已超额，请降低金额，或下月再付", "url": "sysel09q66cbnusv", "doc_id": 229047384, "children": []}, {"title": "Q：为保护你的资金安全，谨防兼职刷单、先支付后返利、免费送等骗局。请谨慎确认交易目的，点击“申请解除限制”继续支付。", "url": "pekhemb58grhafm2", "doc_id": 227802118, "children": []}, {"title": "Q：该设备无此交易权限", "url": "irpkvqnbl37tek9c", "doc_id": 227436363, "children": []}, {"title": "Q：[100302]公众号与商户号不存在绑定关系", "url": "iw89bobbqaecrfr5", "doc_id": 227435506, "children": []}, {"title": "Q：[102]杈限不足", "url": "xmwhfe015blaoqld", "doc_id": 227434877, "children": []}, {"title": "Q：[100302]复合域[terminal info]缺少[device_type]字段(UPO8A5100)", "url": "tbydpfg4m8xb0sfg", "doc_id": 227433007, "children": []}, {"title": "Q：收银员账号已失效，请确认(4009)", "url": "sg1k43awxvag5v2s", "doc_id": 227431987, "children": []}, {"title": "Q：商家当日充值活动额度已满，请联系商家提额或改日", "url": "rnuaegoug2pk2zo9", "doc_id": 227418573, "children": []}, {"title": "Q：商家状态异常，请及时联系老板处理", "url": "zgdrssdmmv0syxlx", "doc_id": 227401641, "children": []}, {"title": "Q：单次输入金额不超过100000元", "url": "tmbela7hu1kmb50u", "doc_id": 227300867, "children": []}, {"title": "Q：[100302]当前商户需补齐相关资料后，才可进行相应的支付交易，请商户联系对接的微信支付服务商", "url": "bn5paucohwfshwsa", "doc_id": 227300582, "children": []}, {"title": "Q：商户进件审核未通过", "url": "gmno9ewltm5206zg", "doc_id": 227262351, "children": []}, {"title": "Q：[100302]无效终端", "url": "uerfm7gkb6kviiv4", "doc_id": 227057647, "children": []}, {"title": "Q：当前交易异常，为保障资金安全，本次交易暂时无法完成", "url": "gxa9aotfr9chpmq7", "doc_id": 227052692, "children": []}, {"title": "Q：支付机构商编获取失败", "url": "wt0iaq48xzmcvgez", "doc_id": 226957961, "children": []}, {"title": "Q：门店不存在或未上架", "url": "agb2x4qrfu2n4a1k", "doc_id": 225916181, "children": []}, {"title": "Q：银联二维码交易未开通", "url": "ofg82t5yx5s7nkz5", "doc_id": 225969129, "children": []}, {"title": "Q：门店信息加载中，请稍等", "url": "fu9hsuv42e207091", "doc_id": 225574280, "children": []}, {"title": "Q:网络繁忙，请稍后再试", "url": "iz92by10qrsggea7", "doc_id": 222530978, "children": []}, {"title": "Q:该商家未在微信支付平台完成开户意愿确认，今日暂无法继续收款", "url": "oe01k1k30my8vgc4", "doc_id": 222531011, "children": []}, {"title": "Q:物业.房地产不支持信用卡支付", "url": "egnx372aqs9lpok9", "doc_id": 222530993, "children": []}, {"title": "Q:交易路由-获取通道商户失败-L", "url": "orl0mvnixwocxzi7", "doc_id": 222530986, "children": []}, {"title": "Q:收款失败，请重新发起支付(5010)", "url": "kuxw89gq6gwovlou", "doc_id": 222530970, "children": []}, {"title": "Q:商户传入的appid参数不正确，请\n联系商户处理", "url": "uy2pkmpgezw4gvpm", "doc_id": 222530963, "children": []}, {"title": "Q：电话手表二维码不能支付", "url": "szre94t0b65x8f4r", "doc_id": 222530957, "children": []}, {"title": "Q：sub_mch_id与sub_appid不匹配", "url": "dfwe8cm6d6h20uo2", "doc_id": 222530947, "children": []}, {"title": "Q：[90303]接口返回值为空", "url": "irvz6h35pmvwprvg", "doc_id": 222530934, "children": []}]}, {"title": "支付宝交易报错QA", "url": "qmox469vu7rblrpp", "doc_id": 225581769, "children": [{"title": "Q：本笔交易金额超过该商家单笔收款最高限额", "url": "gt3124kbp8buyqvz", "doc_id": 229058715, "children": []}, {"title": "Q：客户端IP地址不在白名单中(*************)", "url": "ie2wgktouln5wguh", "doc_id": 228391551, "children": []}, {"title": "Q：该商家历史交易有风险，被暂时关闭收款功能，请稍后再试或改天再付", "url": "ea3v3151mdthr3zx", "doc_id": 228250757, "children": []}, {"title": "Q：该商家今日累计收款已超限额，请改天再付", "url": "tpztgh0f25ncg623", "doc_id": 227424731, "children": []}, {"title": "Q：生效费率不存在", "url": "sbupmlxbedgc1de4", "doc_id": 227411166, "children": []}, {"title": "Q：商户已注销或被冻结", "url": "kag8ohx3gncgtt3u", "doc_id": 226272183, "children": []}, {"title": "Q：调用清算平台错误", "url": "wnn7x3z0yfg67ad4", "doc_id": 226151025, "children": []}, {"title": "Q：受理商户状态异常", "url": "al90yx7b7ro6ypmh", "doc_id": 226142977, "children": []}, {"title": "Q：[5029][ACQ.ACCESS_FORBIDDEN]支付失败，本商户没有权限使用该产品，请联系管理员处理，建议顾客使用其他方式付款、IACCESS FORBIDDENI", "url": "kny05n9rz3gnn0hu", "doc_id": 225581863, "children": []}]}, {"title": "云闪付交易报错QA", "url": "zmtqt9hgyxwcspyh", "doc_id": 225450774, "children": [{"title": "Q：该地区暂不支持银联二维码交易", "url": "cnu1tso7moxktuqs", "doc_id": 227917523, "children": []}, {"title": "Q：[2011]该商户未报备，请稍后重试或联系客服", "url": "dciuxigch3vpuogx", "doc_id": 227914696, "children": []}, {"title": "Q：[90302]银联二维码交易未开通", "url": "ygngiem48zpgxozr", "doc_id": 227408813, "children": []}, {"title": "Q：交易路由-获取通道商户失败-L", "url": "vabvt5ih2kxgdiq7", "doc_id": 226958216, "children": []}, {"title": "Q：交易超过该商家的信用卡支付限额，需商户联系收单机构确认，请学试换卡支付或降低金额付款", "url": "oow7w9kw2w8hz1rd", "doc_id": 225626562, "children": []}, {"title": "Q：请使用支付宝、维信完成支付", "url": "shd2975afvsgpf9x", "doc_id": 225450911, "children": []}]}, {"title": "开放平台报错QA", "url": "nk3tgg1a27zrnngo", "doc_id": 225577242, "children": [{"title": "Q：微信登录失败 redirect_uri域名与后台配置不一致，错误码：10003", "url": "mdy0lbywh35zgf5d", "doc_id": 228396821, "children": []}, {"title": "Q：sub_mch_id与sub_appid不匹配", "url": "cgd758r64b8w6t38", "doc_id": 225588276, "children": []}, {"title": "Q：【无效参数】参数名:merchant_name,原因:may notbe null", "url": "xgrd0i85293by60s", "doc_id": 225584407, "children": []}, {"title": "Q：此公众号并没有这些 scope的权限，错误码:10005", "url": "my5hazfg6hlz04ny", "doc_id": 225584000, "children": []}, {"title": "Q：配置商户的小程序/公众号APPID时候 提示该APPID认证主体与特约商户、所属渠道商或服务商主体不一致，请检查后再试", "url": "ov6v3ybwvg6wfgwt", "doc_id": 225583258, "children": []}, {"title": "Q：老板已将门店隐藏，您无法进行支付", "url": "bgbc75lucv6ks5g2", "doc_id": 225577361, "children": []}]}]}, {"title": "预授权报错QA", "url": "vxin3qiv23dmkw4o", "doc_id": 225590782, "children": [{"title": "Q：该订单已预授权冻结超过30天,无\n法操作", "url": "udrq3sy7m1x56mg1", "doc_id": 225590915, "children": []}]}, {"title": "退款报错QA", "url": "rql1moivi7517kzd", "doc_id": 225473620, "children": [{"title": "Q：退款金额超过今日实收金额,暂无法退款", "url": "scm9l4ilgdm1bgqc", "doc_id": 227803667, "children": []}, {"title": "Q：商户账户余额退款失败", "url": "gcyqs009iw2oq3yd", "doc_id": 227439089, "children": []}, {"title": "Q：该订单正在审核中，不能退款(96)", "url": "ure3xvgqfth1mo21", "doc_id": 227438083, "children": []}, {"title": "Q：[60303]9532713784 商户已注销", "url": "qqt930o4bkmkgpyf", "doc_id": 227396686, "children": []}, {"title": "Q：退款清求失败，用户账号注销，请核实后重试", "url": "beco2rqlr521cnn0", "doc_id": 225971501, "children": []}, {"title": "Q：您的账户余额不足,暂无法退款", "url": "hmrhkmwfrhr6cck2", "doc_id": 225478624, "children": []}, {"title": "Q ：渠道信息异常:商户无该交易接口使用权限", "url": "knyuwxqgf0a5e10z", "doc_id": 225473697, "children": []}, {"title": "Q：该订单支付时间超出可退款时间", "url": "gp8llttkzdsgg4vz", "doc_id": 225584845, "children": []}]}, {"title": "佣金报错QA", "url": "uvb6lx4oaawbcqn7", "doc_id": 225965202, "children": [{"title": "Q：第1行商品简称不合法,合法值为:信息系统服务,请检查!requestld: f90f27e69d1a11e6", "url": "xt3tnvo21atuvpv2", "doc_id": 229013242, "children": []}, {"title": "Q：由于您展业存在高风险行为，佣金暂停结算", "url": "zo6vwp5i8488mz12", "doc_id": 225965253, "children": []}]}, {"title": "乐刷分账报错QA", "url": "khfl6falwgo9ehtd", "doc_id": 225587186, "children": [{"title": "Q：商户进件失败-所用法人身份证注册次数已达上限", "url": "vp666yzu86gpi5i9", "doc_id": 229341812, "children": []}, {"title": "Q：[9996]收款方创建已达上限，请解绑存量收款方", "url": "eg2msuop5lvlwpls", "doc_id": 228388072, "children": []}, {"title": "Q：提现秒到商户状态异常", "url": "xuxk14rlh2k6wlxa", "doc_id": 225587289, "children": []}]}, {"title": "提现银行卡报错QA", "url": "kl1ouwfazvichs8p", "doc_id": 226445351, "children": [{"title": "Q：[1051]上传银行卡非法人本人银行卡或银行卡信息有误，请核对修改后重新提交", "url": "of9xsv5d3bwbkbbk", "doc_id": 227428258, "children": []}, {"title": "Q：暂无该银行卡所属银行，请联系客服添加银行信息后重试", "url": "wdguz6y2cfdnuwxv", "doc_id": 227426807, "children": []}, {"title": "Q：[90302][90302]结算卡bin不符合落地机构准入卡要求!", "url": "xgvep2vudpdkv6et", "doc_id": 227395370, "children": []}, {"title": "Q：商户信息修改失败-修改结算卡次数超限", "url": "xrxitpby7ys3ebms", "doc_id": 227232044, "children": []}, {"title": "Q：商户信息修改失败-暂不支持修改，请联系客服或运营", "url": "seax4sdxwupwxva1", "doc_id": 226445404, "children": []}]}, {"title": "切通道报错QA", "url": "ptexknkf0npvg6t6", "doc_id": 225926550, "children": [{"title": "Q：目标通道微信费率与当前通道不一致", "url": "il37aluhcc299i18", "doc_id": 225926617, "children": []}]}, {"title": "收款单QA", "url": "ryi9e9dxk8dylseo", "doc_id": 225923646, "children": [{"title": "Q：暂无权限可联系管理员处理", "url": "bmoha1xyhkomkp50", "doc_id": 225930688, "children": []}, {"title": "Q：暂不支持系统商户开通", "url": "goqqa4avow<PERSON>ual", "doc_id": 225925717, "children": []}]}, {"title": "会员QA", "url": "igi7nheotg5znb0h", "doc_id": 229365734, "children": [{"title": "Q：充值金额必须大于激活门槛", "url": "creoxugtxn8rsdzc", "doc_id": 229365819, "children": []}]}]}, {"title": "活动政策", "url": "", "doc_id": "", "children": [{"title": "公立医院", "url": "zc11wvfsv7p3f3d2", "doc_id": 228053154, "children": [{"title": "微信公立医院", "url": "saa29mlwbnphq1gy", "doc_id": 228050256, "children": []}, {"title": "支付宝公立医院", "url": "eho7ulwm8z9rt4ep", "doc_id": 228053099, "children": []}]}, {"title": "银联商户号/银联商编", "url": "ehvdknp1bv36me0q", "doc_id": 226405961, "children": []}, {"title": "国补", "url": "qlv4a3wgrru1m5v6", "doc_id": 226403116, "children": [{"title": "国补权限开通", "url": "bcclbuxuy5stbq2n", "doc_id": 226403654, "children": []}, {"title": "国补添加商品", "url": "pvczg9gx5if2fcyd", "doc_id": 226408307, "children": []}, {"title": "国补商品下单", "url": "ohp2valvaezbk7g5", "doc_id": 226410386, "children": []}, {"title": "国补商品退款", "url": "obsgcewhqy0lsrs2", "doc_id": 226411720, "children": []}, {"title": "商户审计", "url": "ca8ecy9g5szro8ga", "doc_id": 226413330, "children": []}, {"title": "国补FAQ", "url": "lh5umc5ohmvt909v", "doc_id": 226282910, "children": []}]}, {"title": "丰收计划", "url": "dweltqax84fqysui", "doc_id": 225592593, "children": [{"title": "硬件积分明细", "url": "hbxgx8ulvbm81lo7", "doc_id": 225592742, "children": []}]}, {"title": "银行活动（金卡）", "url": "", "doc_id": "", "children": [{"title": "金卡活动QA", "url": "llygi656v86c6qqg", "doc_id": 226427558, "children": []}, {"title": "付呗APP查询银行活动补贴的流程", "url": "fli4nggfpsh8mqfq", "doc_id": 227298087, "children": []}, {"title": "金卡报名流程", "url": "hmgdgnmd72267zhp", "doc_id": 225165294, "children": []}, {"title": "易生通道", "url": "", "doc_id": "", "children": [{"title": "易生金卡报名常见QA", "url": "zby340gbk2kszs75", "doc_id": 221261194, "children": []}]}, {"title": "乐刷通道", "url": "", "doc_id": "", "children": [{"title": "乐刷通道报名QA", "url": "wwkdb98rccco1gn4", "doc_id": 223566672, "children": []}, {"title": "乐刷-额度包查询", "url": "sxrrgk09f0hdy2eb", "doc_id": 221446925, "children": []}]}, {"title": "国通通道", "url": "", "doc_id": "", "children": [{"title": "常见问题QA", "url": "xtox3pt7zvgbvyus", "doc_id": 220532862, "children": []}, {"title": "国通-额度包查询", "url": "gpz9x55ezfg9hlef", "doc_id": 220435017, "children": []}]}, {"title": "富友通道", "url": "", "doc_id": "", "children": [{"title": "常见问题QA", "url": "kadl9ge6bvau41ux", "doc_id": 220359534, "children": []}, {"title": "富友-额度包查询", "url": "bh44xold6rafudgk", "doc_id": 220434869, "children": []}, {"title": "富友-后台报名的流程", "url": "https://fshows.yuque.com/rc067d/htffni/tnsc8oaa42s26uxs?singleDoc# 《富友-后台报名的流程》", "doc_id": "", "children": []}, {"title": "富友-常规额度配置流程", "url": "https://fshows.yuque.com/rc067d/htffni/tfv5irn2ql0xhq2z?singleDoc# 《富友-常规额度配置流程》", "doc_id": "", "children": []}]}]}, {"title": "云闪付", "url": "aa2iz7qg2w89yho2", "doc_id": 221287624, "children": [{"title": "云闪付各通道禁开地区", "url": "zv1tbvnudo2n91t7", "doc_id": 227916946, "children": []}]}]}, {"title": "商户进件", "url": "", "doc_id": "", "children": [{"title": "催审以及审核时效", "url": "gg7n7efgcfm5exp0", "doc_id": 220715489, "children": []}, {"title": "进件资料", "url": "", "doc_id": "", "children": [{"title": "进件签约", "url": "sgz70co5wpplbl8c", "doc_id": 228221159, "children": []}, {"title": "进件流程", "url": "", "doc_id": "", "children": [{"title": "司南端进件流程", "url": "hxppduwz08zxxl07", "doc_id": 220357207, "children": []}, {"title": "代理商网页进件流程", "url": "xztehso0fecz0uob", "doc_id": 220375404, "children": []}, {"title": "特殊资料补充", "url": "xz3lmf6bsdvhrf7f", "doc_id": 229199751, "children": []}]}, {"title": "乐刷巡检表", "url": "pgysh1pv2s974x90", "doc_id": 224051649, "children": []}, {"title": "手机号进件次数限制及解决方案", "url": "wtw8pyapmdkuv03l", "doc_id": 221432230, "children": []}, {"title": "商户自助进件流程", "url": "oxzm0vmawu1rekzn", "doc_id": 220438460, "children": []}, {"title": "异地开户", "url": "rtuvnzpvbhy3g8hq", "doc_id": 220437768, "children": [{"title": "查询商户是否异地开户的步骤", "url": "zwmmqa6e3tdl1z27", "doc_id": 222281898, "children": []}, {"title": "异地开户催审流程", "url": "kpx3rb1x66pt7q1q", "doc_id": 222281185, "children": []}]}, {"title": "开户次数限制", "url": "qlloaq4tf38a5e50", "doc_id": 220437720, "children": [{"title": "乐刷-入网次数超限报备流程", "url": "sfaicp0qrzmx8r8k", "doc_id": 220445638, "children": []}]}, {"title": "各通道禁止入网地区", "url": "bvqzv45yxcvl2cg9", "doc_id": 220405958, "children": []}, {"title": "各通道禁止开通刷卡地区", "url": "hd7x5y4wn01m6aop", "doc_id": 220409638, "children": []}, {"title": "多通道进件", "url": "rw378bkqbflayoa6", "doc_id": 220407082, "children": []}, {"title": "入网规则 （珠宝 汽车行业限制）", "url": "kplg4zun4wrccgxg", "doc_id": 220406945, "children": []}, {"title": "港澳台客户进件/事业单位", "url": "oxdxdgd8ts95tn2k", "doc_id": 220402151, "children": []}, {"title": "各机构入网商户年龄限制", "url": "ggxxc0hz0gv5hnqh", "doc_id": 220397051, "children": []}, {"title": "非法人授权书模板", "url": "uhbl99izu6wc1k2a", "doc_id": 220393054, "children": []}, {"title": "刷卡支持装修贷的类目", "url": "xrwg18ue4ead5a6g", "doc_id": 228245332, "children": []}]}, {"title": "进件驳回", "url": "", "doc_id": "", "children": [{"title": "乐刷-商户入网敏感词解除解决方案", "url": "ui3sdb4d053pwqy5", "doc_id": 220445531, "children": []}, {"title": "乐刷-常见审核驳回及解决方案", "url": "qwsmhlg6iepsavay", "doc_id": 220445484, "children": []}]}, {"title": "实名认证", "url": "", "doc_id": "", "children": [{"title": "微信认证", "url": "ph7rgr6di0d5rlmn", "doc_id": 220400940, "children": [{"title": "自有渠道认证", "url": "qe4ausu4g3d1dsrb", "doc_id": 228085159, "children": []}, {"title": "微信认证背景", "url": "dnqzp2qa1fv6f7qn", "doc_id": 220581672, "children": []}, {"title": "微信认证拓展二维码", "url": "huaads6a898sgyg2", "doc_id": 220402722, "children": []}, {"title": "微信认证如何选择认证主体类型", "url": "ti895tmggi47<PERSON>zy", "doc_id": 220405001, "children": []}, {"title": "线下微信认证操作流程", "url": "dd0upygfccf3tm3s", "doc_id": 220593152, "children": []}, {"title": "司南微信认证流程", "url": "ps6b8kar262tg7kr", "doc_id": 220736365, "children": []}, {"title": "微信认证FAQ", "url": "gbgnsg43xgyb35gp", "doc_id": 220776534, "children": []}]}, {"title": "支付宝认证", "url": "ludldgckvbons0w1", "doc_id": 220400981, "children": [{"title": "支付宝认证背景", "url": "wpc13ootu60wkvgd", "doc_id": 220778650, "children": []}, {"title": "支付宝认证拓展码", "url": "exiuqyuom9e0ak1t", "doc_id": 220779283, "children": []}, {"title": "不同主体类型所需资料 /授权方式", "url": "oavbqqwcsnzwg4d1", "doc_id": 220778848, "children": []}, {"title": "线下支付宝认证操作流程", "url": "tx526f31qktsyqgv", "doc_id": 220779638, "children": []}, {"title": "司南端支付宝认证操作流程", "url": "ukeog5agzqc9tzb8", "doc_id": 220786054, "children": []}, {"title": "支付宝认证FAQ", "url": "mkbs8dth8h1l4pt0", "doc_id": 220925340, "children": []}]}]}, {"title": "资料修改", "url": "", "doc_id": "", "children": [{"title": "乐刷邮件规范", "url": "ablm9g5vxyvralox", "doc_id": 229196815, "children": []}, {"title": "小微升级营业执照", "url": "hmv6oygg2ak5ids8", "doc_id": 228373747, "children": []}, {"title": "客服驳回操作", "url": "cabxk4gzlltgzdgx", "doc_id": 227228262, "children": []}, {"title": "通道切换", "url": "vcubkgk3nswee1qh", "doc_id": 220710396, "children": []}, {"title": "修改营业执照", "url": "ffm6ai9cdfxcqivg", "doc_id": 220706861, "children": []}, {"title": "修改银行卡", "url": "rk9rq0pwk5htcepz", "doc_id": 220678345, "children": []}, {"title": "修改结算人", "url": "ah6mhqgsv0rgeud3", "doc_id": 220673641, "children": []}, {"title": "修改身份证", "url": "bddfielgz6pr61ln", "doc_id": 220611767, "children": []}, {"title": "修改门店经营地址", "url": "", "doc_id": "", "children": [{"title": "代理商网页后台修改流程", "url": "lwfeagsddf0h5rpz", "doc_id": 220441525, "children": []}, {"title": "司南APP修改流程", "url": "qn0m2zgep6eh3lng", "doc_id": 220272322, "children": []}, {"title": "付呗APP修改流程", "url": "rluc4xc6h3vomelc", "doc_id": 220442032, "children": []}, {"title": "付呗后台修改流程", "url": "zxo4esqgqan1po86", "doc_id": 220441491, "children": []}]}, {"title": "修改营业执照地址", "url": "", "doc_id": "", "children": [{"title": "司南APP修改流程", "url": "obxmkhgw3eymxp9g", "doc_id": 220444187, "children": []}, {"title": "网页端修改流程", "url": "vxq4d363bv3oklon", "doc_id": 220270539, "children": []}]}, {"title": "修改门店信息", "url": "", "doc_id": "", "children": [{"title": "付呗APP修改流程", "url": "grm2rrm1qatg2u7v", "doc_id": 220442925, "children": []}]}, {"title": "修改支付后简称", "url": "bfyxtwlv6hkndehe", "doc_id": 220275863, "children": [{"title": "支付后名称展示说明", "url": "zcxx88rdmlc1zopr", "doc_id": 220444828, "children": []}]}]}, {"title": "支付牌照", "url": "gziaz9f376zt51g9", "doc_id": 226970670, "children": [{"title": "银盛支付牌照", "url": "kwfv3gxu8qv48uow", "doc_id": 227977327, "children": []}, {"title": "拉卡拉止付牌照", "url": "gx8pgq1pa9iycbx8", "doc_id": 227972261, "children": []}, {"title": "国通支付牌照", "url": "wu80caqvz9407u0z", "doc_id": 227971941, "children": []}, {"title": "富友支付牌照", "url": "gz5b3sm3di38rg5x", "doc_id": 227971178, "children": []}, {"title": "易生支付牌照", "url": "sr5y7y1mo4xblzvu", "doc_id": 226970980, "children": []}]}]}, {"title": "付呗司南", "url": "mb77hazg2zy36bpp", "doc_id": 221129162, "children": [{"title": "司南下载登录", "url": "pk732eo9suhw1g6p", "doc_id": 228048693, "children": []}, {"title": "工作台", "url": "qgkmly9i0u9ydngs", "doc_id": 221204783, "children": []}, {"title": "商户板块", "url": "aeoskp2tyvi3ku3u", "doc_id": 221231533, "children": [{"title": "终端信息", "url": "qt4oxzrn4edlqcri", "doc_id": 227120999, "children": [{"title": "终端换绑规则", "url": "iq39hbl3p8gpe2tl", "doc_id": 227121467, "children": []}]}, {"title": "费率", "url": "fn0f5k463u1rbdf1", "doc_id": 225913711, "children": []}, {"title": "通道切换", "url": "hp9xwerz5rweh1ys", "doc_id": 225903059, "children": []}, {"title": "微信支付线上通道", "url": "deblgsoy52blbsos", "doc_id": 225608795, "children": []}]}, {"title": "数据", "url": "hhqcwwax9udhbxbh", "doc_id": 221263685, "children": []}, {"title": "我的", "url": "dalkgcdi95coktdi", "doc_id": 221270199, "children": []}, {"title": "受理商FAQ", "url": "zxgx4sg2kqy5cicg", "doc_id": 225449764, "children": []}]}, {"title": "代理商后台", "url": "ufr88yokxa5hswvw", "doc_id": 227076595, "children": [{"title": "登录页面", "url": "gmdgflhi07clibv2", "doc_id": 227077854, "children": []}, {"title": "首页", "url": "cqqolvo8upukr0ex", "doc_id": 227076718, "children": []}, {"title": "代理商学院", "url": "gwr78p5aayprtxit", "doc_id": 227077103, "children": []}, {"title": "常用功能", "url": "fvr796owy9mibxse", "doc_id": 227077169, "children": [{"title": "商户管理", "url": "tkcevdp6b83attea", "doc_id": 227079861, "children": []}, {"title": "门店管理", "url": "rckb3fy5puxvfpe5", "doc_id": 227079915, "children": []}, {"title": "受理商管理", "url": "gsif5724go2ix4b2", "doc_id": 227080061, "children": []}, {"title": "市场经理管理", "url": "yx7owsr8t5b6n688", "doc_id": 227080188, "children": []}, {"title": "回客餐饮管理", "url": "exd940n762iu455e", "doc_id": 227080245, "children": []}, {"title": "友店旗舰版管理", "url": "ih39obvxzv0om6gp", "doc_id": 227080322, "children": []}]}, {"title": "交易数据", "url": "lzvpd316pol7d4er", "doc_id": 227077228, "children": [{"title": "交易分佣", "url": "ma3qoe2c4adlqwv1", "doc_id": 227081451, "children": []}, {"title": "佣金结算", "url": "mos3utxxbzblso0o", "doc_id": 227081418, "children": [{"title": "佣金FAQ", "url": "ubrmkviaa1ogf75c", "doc_id": 227971581, "children": []}]}, {"title": "丰收计划数据", "url": "gbk6i5rii7ceawkb", "doc_id": 227081092, "children": []}, {"title": "金卡行动", "url": "uhol9adb5r3wgvd5", "doc_id": 227081012, "children": []}, {"title": "支付宝小程序数据", "url": "mgq6serwe7ung05c", "doc_id": 227080934, "children": []}, {"title": "微信直连分佣", "url": "utboqdqcsbfxpsg7", "doc_id": 227080882, "children": []}, {"title": "微信直连商业版分佣", "url": "gm49qt66kifieulh", "doc_id": 227080571, "children": []}, {"title": "刷脸活动佣金", "url": "pwod3eh0fcqcfdoc", "doc_id": 227080522, "children": []}]}, {"title": "其他", "url": "egvu1id8bq7wt71f", "doc_id": 227077259, "children": [{"title": "硬件解绑", "url": "opyspss3tkfmzaxl", "doc_id": 227251681, "children": []}, {"title": "支付宝刷脸活动", "url": "raqnoab66o0s3x3n", "doc_id": 228414469, "children": []}, {"title": "蜻蜓广告（物料管理）", "url": "tx5ry1l0pdrg9k52", "doc_id": 228414331, "children": []}, {"title": "蜻蜓广告（广告投放）", "url": "yibpi8sg2ng22tps", "doc_id": 228418680, "children": []}, {"title": "系统公告", "url": "rxe8a0dxabk43fhi", "doc_id": 228413505, "children": []}, {"title": "微信直连商户管理", "url": "omn0iz8x89d7z8sz", "doc_id": 228413292, "children": []}, {"title": "支付宝直连商户管理", "url": "cavg4y6dz09vg8da", "doc_id": 228413195, "children": []}, {"title": "微信高校食堂资料提交", "url": "gczc2v6u1dcob4bv", "doc_id": 228413063, "children": []}, {"title": "受理商开户业绩", "url": "cvwbwq1mq2mowbbw", "doc_id": 228411538, "children": []}, {"title": "微信直连交易订单", "url": "zoypk77bncbpvmgp", "doc_id": 228411328, "children": []}, {"title": "支付宝直连交易订单", "url": "riofl5u56ity5g66", "doc_id": 228411113, "children": []}, {"title": "超级受理商佣金", "url": "ocuc1saxwam3mk7b", "doc_id": 228411031, "children": []}, {"title": "收款通道统计", "url": "hb6r14dg1z067n97", "doc_id": 228410915, "children": []}, {"title": "会员数据", "url": "gkaep8go0emrullh", "doc_id": 228410838, "children": []}, {"title": "会员系统追踪", "url": "qdf9gyz2xqzax7gz", "doc_id": 228410743, "children": []}, {"title": "流失门店管理", "url": "iumu66oe2s14g5i4", "doc_id": 227251277, "children": []}, {"title": "日志管理", "url": "pkbv4k69a7wtkkcn", "doc_id": 227251093, "children": []}, {"title": "接口配置", "url": "rzg1nlkgvbf7uzyl", "doc_id": 227250504, "children": []}]}]}, {"title": "付呗app", "url": "gfzmiy756om5m5b7", "doc_id": 221445666, "children": [{"title": "App下载登录", "url": "qz1iuh3vkmz94t1b", "doc_id": 221445760, "children": []}, {"title": "App首页", "url": "nmbwe7f826pl816l", "doc_id": 221449497, "children": [{"title": "极速开票", "url": "yabt0gye1tcmiokk", "doc_id": 226283346, "children": [{"title": "开票管理", "url": "ryowugbxzqg11gs4", "doc_id": 226284598, "children": []}, {"title": "消费者发票开通流程", "url": "wg8vs49rcuik7tl0", "doc_id": 226284000, "children": []}, {"title": "消费者发票FAQ", "url": "wx6sux7sqemh59p3", "doc_id": 226284690, "children": []}]}, {"title": "收款", "url": "pekvowkzn5m830uq", "doc_id": 221531160, "children": []}, {"title": "收入统计", "url": "fmivfcuhexeach9t", "doc_id": 221661894, "children": []}, {"title": "实时账单", "url": "fg6x5y5rk8ekm3ya", "doc_id": 224198502, "children": []}, {"title": "花呗分期", "url": "idpqxkzsc7bn321h", "doc_id": 221661793, "children": []}, {"title": "商户贷/个人贷", "url": "od2bgq2hgg33dh1m", "doc_id": 221661697, "children": [{"title": "商户贷FAQ", "url": "pfktt2ggcrhd0unb", "doc_id": 227945274, "children": []}]}, {"title": "活动报名", "url": "ls28rrrzydhz1hi1", "doc_id": 221604811, "children": []}, {"title": "人员招牌", "url": "aa5u4o51uxg2gq7x", "doc_id": 221604670, "children": []}, {"title": "会员", "url": "yeasp31f4zggifqs", "doc_id": 221593952, "children": [{"title": "会员FAQ", "url": "rbgk9cg5r9bhd4rh", "doc_id": 225624118, "children": []}]}, {"title": "数字门店", "url": "mqh2ps4gowsc99oi", "doc_id": 221129263, "children": [{"title": "网店经营", "url": "pxz5x1g6a12tfdzo", "doc_id": 229397229, "children": [{"title": "配送管理", "url": "gqqwqvah5k8lvxhr", "doc_id": 230032330, "children": [{"title": "第三方配送", "url": "ae6klt99qhqufby3", "doc_id": 230060059, "children": []}, {"title": "开通入口", "url": "zn50ysu1sn2qsh7z", "doc_id": 230034603, "children": []}, {"title": "配送设置", "url": "ye4v2ygrgz7ncdxy", "doc_id": 230059254, "children": []}, {"title": "配送充值", "url": "iypik0ql6tmll29z", "doc_id": 230052326, "children": []}]}, {"title": "店铺装修", "url": "ianck1bmzodnp17g", "doc_id": 230032241, "children": []}, {"title": "基础设置", "url": "fy7ee4btgmwtahq0", "doc_id": 229397459, "children": [{"title": "打包费设置", "url": "yf39tff3ybtlsw35", "doc_id": 230031138, "children": []}, {"title": "码管理", "url": "xwhgb9fknkkedw68", "doc_id": 230029932, "children": []}, {"title": "取单方式", "url": "xy47kk1606xnvzv4", "doc_id": 230030350, "children": []}]}]}, {"title": "营销工具", "url": "qhlvmutlqwagt2ga", "doc_id": 229371091, "children": [{"title": "会员招募", "url": "ak8rhkwy48a3u6vb", "doc_id": 229371240, "children": [{"title": "设置入口", "url": "gs4ou28b5mwggymq", "doc_id": 230019261, "children": []}, {"title": "活动设置", "url": "psgyagtnyy6y8mc5", "doc_id": 230019433, "children": []}, {"title": "删除活动", "url": "bnq8us92zcxx92h5", "doc_id": 230022482, "children": []}]}, {"title": "优惠券", "url": "rb5chicm5v8uk37r", "doc_id": 229382731, "children": []}, {"title": "团购接龙", "url": "giicb5ag7kmelhef", "doc_id": 229384364, "children": []}, {"title": "特价活动", "url": "dz13hmsqs33swmrk", "doc_id": 229390765, "children": []}, {"title": "好友神器", "url": "uck7kyymie54vw79", "doc_id": 229393176, "children": []}, {"title": "海报中心", "url": "rh56wdhwvx8u47s3", "doc_id": 229396746, "children": []}]}, {"title": "第三方配送FAQ", "url": "gdhcgvw24l3cmuc9", "doc_id": 228058551, "children": []}, {"title": "桌码", "url": "sawg750ptewue8at", "doc_id": 227117404, "children": []}, {"title": "集团点餐码", "url": "phk0ab70h1pbsowg", "doc_id": 224657971, "children": []}, {"title": "扫码点餐团餐功能（先吃后付）", "url": "ylmemda18f7p2ggc", "doc_id": 224650741, "children": []}, {"title": "小程序FAQ", "url": "mqwmmi4ss86h6wx0", "doc_id": 225624519, "children": []}]}, {"title": "收款单", "url": "kzu98cmxy6zgha46", "doc_id": 221594212, "children": [{"title": "收款单配置APPID收款", "url": "nrex2aosdcfublin", "doc_id": 227226577, "children": []}]}, {"title": "海报中心", "url": "arxvzpumfpgtyong", "doc_id": 221678192, "children": []}]}, {"title": "账单", "url": "ys5dh0ssak3013bs", "doc_id": 221689136, "children": [{"title": "商户联系消费者流程", "url": "qzuw54l0ztnuckve", "doc_id": 227937178, "children": []}, {"title": "退款", "url": "ng7b8wkcd693a9gm", "doc_id": 225473555, "children": []}]}, {"title": "发现", "url": "eykcyc8mni3yy1or", "doc_id": 221691224, "children": []}, {"title": "我的页面", "url": "tyso6sa7td3q9xyn", "doc_id": 221691970, "children": [{"title": "商户信息", "url": "zuxznlrqyvatdzaw", "doc_id": 221703325, "children": []}, {"title": "员工管理", "url": "mewbpmzl98g4at4b", "doc_id": 221710753, "children": [{"title": "员工FAQ", "url": "atroy3rzwoakyb5b", "doc_id": 226113200, "children": []}]}, {"title": "门店二维码", "url": "mt9kks7awkkxgu88", "doc_id": 221717821, "children": []}, {"title": "设备管理", "url": "gp4n2wu615we134w", "doc_id": 221718971, "children": []}, {"title": "语音设置", "url": "gdrc3abu1ppq8p0n", "doc_id": 221722718, "children": []}, {"title": "打印设置", "url": "vzg6nq7l0mlcb72v", "doc_id": 221726294, "children": []}, {"title": "当前版本", "url": "thuktg0z249bo1h7", "doc_id": 221727311, "children": []}, {"title": "联系客服", "url": "hwborzktfdv1t8hn", "doc_id": 221727461, "children": []}, {"title": "帮助视频", "url": "nyzzq9i2lckr86iq", "doc_id": 221728912, "children": []}, {"title": "意见反馈", "url": "mqpsldcrcqeenyms", "doc_id": 221729376, "children": []}]}]}, {"title": "商户后台", "url": "", "doc_id": "", "children": [{"title": "登录页面", "url": "ucfnkgl0tw0nn1to", "doc_id": 228277927, "children": []}, {"title": "对账中心", "url": "nqi7fkvdg4u6ktn6", "doc_id": 221232696, "children": []}, {"title": "资金管理", "url": "ogn1ip7g6dfkrfpv", "doc_id": 221257134, "children": []}, {"title": "商户手续费发票", "url": "", "doc_id": "", "children": [{"title": "钉钉申请手续费发票流程", "url": "ibt4vdsbbp8y6iir", "doc_id": 220920947, "children": []}, {"title": "后台申请手续费发票流程（12月内乐刷和随行付）", "url": "swgdiaokbzhwy97r", "doc_id": 220920860, "children": []}, {"title": "手续费发票FAQ", "url": "qgzg9omxymg8q6ra", "doc_id": 225579360, "children": []}]}, {"title": "运营中心", "url": "wi92e7gyyhry27sx", "doc_id": 221357740, "children": [{"title": "会员列表-商品-商品库", "url": "rmsrb10orv7oyamw", "doc_id": 221381526, "children": []}, {"title": "会员列表-库存-库存查询", "url": "hlvmszxyydmlelal", "doc_id": 221406922, "children": []}, {"title": "会员列表-采购", "url": "pcffrq3aq7n5p16y", "doc_id": 221414839, "children": []}, {"title": "会员列表-订单", "url": "unhslmc1g264f6qt", "doc_id": 221416943, "children": []}, {"title": "会员列表-客户-我的会员", "url": "hfkw6thpag47g4xp", "doc_id": 221377775, "children": []}, {"title": "会员列表-财务/营销/设置", "url": "uifzmw3buwpb7ayi", "doc_id": 221420943, "children": []}, {"title": "会员管理-充值记录", "url": "baph6vdwvoqlo9sg", "doc_id": 221422310, "children": []}, {"title": "会员管理-消费记录", "url": "udr4p8292fhzhvaq", "doc_id": 221430681, "children": []}]}, {"title": "商户中心", "url": "fw2ua6oyrf06m7q1", "doc_id": 221432982, "children": []}, {"title": "付呗应用", "url": "lgwxu62ey10n7qc6", "doc_id": 221440818, "children": []}, {"title": "开放平台", "url": "pz0cg587qi1fvu6t", "doc_id": 221443215, "children": []}, {"title": "安全中心", "url": "tzb4m9i8ge6d924b", "doc_id": 221443963, "children": []}]}, {"title": "商户常用功能", "url": "dbvhburrdmu70w55", "doc_id": 228043348, "children": [{"title": "关联多门店", "url": "ioeggwypogxyfmko", "doc_id": 228042116, "children": [{"title": "关联多门店FAQ", "url": "iv8wo9xqy4hcxli6", "doc_id": 228042758, "children": []}]}, {"title": "集团账号", "url": "gltm0sbfr7tnuol8", "doc_id": 227974499, "children": [{"title": "申请入口", "url": "duaz9t8irys11ben", "doc_id": 229428201, "children": []}, {"title": "登录密码", "url": "ky6gm7hegzmw2emq", "doc_id": 229429818, "children": []}, {"title": "集团账号添加员工账号流程", "url": "szi6mu47bp0477gh", "doc_id": 229430985, "children": []}, {"title": "集团对账", "url": "skygzrnrcamu3ud6", "doc_id": 229431188, "children": []}, {"title": "集团账号FAQ", "url": "odykgwodnhf3szn9", "doc_id": 227977704, "children": []}]}]}, {"title": "交易页面支付名称", "url": "rb4kg51zozpgtwp4", "doc_id": 224365811, "children": [{"title": "商户简称", "url": "rg5e5slzfgxwa8zt", "doc_id": 225597965, "children": []}, {"title": "门店名称", "url": "zo4rwoznb4l079wu", "doc_id": 224366049, "children": []}, {"title": "商户全称", "url": "yscukzcfzt5f8dkv", "doc_id": 224366009, "children": []}, {"title": "支付后简称", "url": "hpu439hmzc1niaeb", "doc_id": 224365974, "children": []}]}, {"title": "直连", "url": "gif6inqwvztey35k", "doc_id": 228401937, "children": [{"title": "微信直连", "url": "iopg3tadpiewp0in", "doc_id": 221880600, "children": [{"title": "开通流程", "url": "nwx8afhwrdxun3sb", "doc_id": 225612815, "children": []}, {"title": "微信直连商业版/微信直连 信息查询", "url": "qc5nng3iiy85thmx", "doc_id": 221880677, "children": []}, {"title": "提现记录查询", "url": "zwap31znlp7kv1go", "doc_id": 221881505, "children": []}, {"title": "提现未到账", "url": "dlct0vf0fgxsesz8", "doc_id": 221881227, "children": []}, {"title": "关闭流程", "url": "rgdip4x4umvs60wl", "doc_id": 221881722, "children": []}, {"title": "微信直连FAQ", "url": "kkihanmp6wdm9xg8", "doc_id": 225650977, "children": []}]}, {"title": "支付宝直连", "url": "tzvnvguntfev9kd9", "doc_id": 225610760, "children": [{"title": "开通流程", "url": "dn740bo110ximueg", "doc_id": 225614077, "children": []}, {"title": "关闭流程", "url": "qxtvgdf4xglwtr3z", "doc_id": 225918067, "children": []}, {"title": "支付宝直连FAQ", "url": "bpf2e9p3p2raxo75", "doc_id": 225649739, "children": []}]}]}, {"title": "微信行业活动", "url": "tna4cm1zzvdol6qv", "doc_id": 225593067, "children": [{"title": "教育培训", "url": "him3p4oi0pgik2ee", "doc_id": 226273965, "children": [{"title": "教培报名入口", "url": "rfgrchri13mlzkxc", "doc_id": 226274786, "children": []}, {"title": "报名类目", "url": "um5x6fgbpuqaqewa", "doc_id": 226274727, "children": []}, {"title": "教培FAQ", "url": "rtl4zmtv3y6b3xin", "doc_id": 225595175, "children": []}]}, {"title": "医疗美容/停车", "url": "dcdsdff9xyac5mbl", "doc_id": 226291024, "children": [{"title": "报名入口", "url": "mt8zq6voz71ng0zx", "doc_id": 226291133, "children": []}, {"title": "报名类目", "url": "iwlx8me5q66t3x3s", "doc_id": 226291336, "children": []}, {"title": "医疗美容/停车FAQ", "url": "ffsuikoc73t17i91", "doc_id": 225910745, "children": []}]}]}, {"title": "商户提现", "url": "", "doc_id": "", "children": [{"title": "打款附言", "url": "rg3mt7feheflyxvm", "doc_id": 225964232, "children": []}, {"title": "提现方式", "url": "qmgrdz29vtarsy0y", "doc_id": 225572514, "children": []}, {"title": "无法提现问题客服处理", "url": "xdgcq3xd4q7dufas", "doc_id": 221015886, "children": []}, {"title": "提现补打时间", "url": "gwi31sop85exgggm", "doc_id": 225332586, "children": []}, {"title": "商户提现补打流程", "url": "frv9fgrr5b63p3bn", "doc_id": 221365239, "children": []}, {"title": "分账提现失败查询方式", "url": "puxgmqdra8sewpp8", "doc_id": 221375253, "children": []}, {"title": "提现失败原因及解决方案", "url": "kb48d05t1u53y24v", "doc_id": 221360794, "children": []}, {"title": "提现金额超百万", "url": "ziebt2dp5s85deqf", "doc_id": 221285205, "children": []}, {"title": "到账问题FAQ", "url": "qi06vmv061gbhqmg", "doc_id": 225947101, "children": []}]}, {"title": "风险控制", "url": "rg7op7v1gd3cud34", "doc_id": 217338776, "children": [{"title": "风控日常问题FAQ", "url": "iuanky77z57tq8bv", "doc_id": 224476687, "children": [{"title": "拉卡拉止付申诉", "url": "ti2wfbqf6pp8clbk", "doc_id": 227787574, "children": []}, {"title": "微信小程序投诉", "url": "nlyonqclxkgabdef", "doc_id": 224637850, "children": []}]}, {"title": "公检法调取资料", "url": "ssunoy9k71xq9gna", "doc_id": 224512414, "children": []}, {"title": "风控钉钉群/微信群对接处理", "url": "tlg7otsvh9zgq0rf", "doc_id": 221548809, "children": []}, {"title": "风控商户", "url": "ani9uu4k5eguyvs5", "doc_id": 221443733, "children": []}, {"title": "风控申述", "url": "", "doc_id": "", "children": [{"title": "微信拦截申诉", "url": "rihxwbixiam5kqru", "doc_id": 227229907, "children": []}, {"title": "申诉进度查询", "url": "qkur5s012hmgvk4u", "doc_id": 217660574, "children": []}, {"title": "微信风控", "url": "aslt47qel1rk3l44", "doc_id": 222416204, "children": []}, {"title": "支付宝风控", "url": "vhmedy0gdrp8qu4f", "doc_id": 222418088, "children": []}, {"title": "乐刷通道", "url": "fzpxwyy3do49d2dp", "doc_id": 217633877, "children": []}, {"title": "联动通道", "url": "ai64g8eynrnc92sw", "doc_id": 221532638, "children": []}, {"title": "随行付通道", "url": "pla09rek1w0ltwvm", "doc_id": 221565881, "children": []}, {"title": "拉卡拉通道", "url": "mtdsgezn17vgf4r3", "doc_id": 222408174, "children": []}, {"title": "易生通道", "url": "gwetpug51bx0wnti", "doc_id": 222411236, "children": []}, {"title": "富友通道", "url": "gkk3bfg3txlo22y3", "doc_id": 222413044, "children": []}, {"title": "国通通道", "url": "guqyespegsg9o6ru", "doc_id": 222414966, "children": []}]}, {"title": "报错类型", "url": "", "doc_id": "", "children": [{"title": "商户进件报错+处理方案", "url": "wuo098gdm3ungfwo", "doc_id": 221446902, "children": []}, {"title": "交易报错+处理方案", "url": "bne0k2pvrcn5kfrs", "doc_id": 220917566, "children": []}]}, {"title": "整改", "url": "", "doc_id": "", "children": [{"title": "机具异常整改", "url": "mawgf1t4li6knnuu", "doc_id": 220919028, "children": []}, {"title": "营业执照到期整改", "url": "fzoka6403bnv8vky", "doc_id": 220918979, "children": []}, {"title": "身份证到期整改", "url": "yi5f28zzaygiogx4", "doc_id": 220918939, "children": []}]}, {"title": "调单", "url": "", "doc_id": "", "children": [{"title": "其他调证流程", "url": "vuz9ggsdqo9oaw1k", "doc_id": 220918841, "children": []}, {"title": "银联调单（乐刷）", "url": "xy6xk25l9vlfxmlc", "doc_id": 220918779, "children": []}, {"title": "公检法调证流程", "url": "ih8mif5q3lw5v3a5", "doc_id": 220918719, "children": []}]}, {"title": "商户注销", "url": "", "doc_id": "", "children": [{"title": "易生通道注销流程", "url": "eotsfx4ak3bkiie1", "doc_id": 220918103, "children": []}, {"title": "乐刷通道注销流程", "url": "hu3m0vmuvkvydk1e", "doc_id": 220917934, "children": []}, {"title": "付呗商户注销流程", "url": "te9l0sg5xb5opggk", "doc_id": 220917797, "children": []}]}, {"title": "代理商违规", "url": "", "doc_id": "", "children": [{"title": "受理商处理黑名单", "url": "hp05gq7noqynckxv", "doc_id": 226090356, "children": []}, {"title": "代理商处罚标准", "url": "rsii8w2z3rpx7tvz", "doc_id": 220918468, "children": []}, {"title": "运营中心受理商违规处罚标准", "url": "iamu87g2h36do813", "doc_id": 220918511, "children": []}]}, {"title": "切通道流程", "url": "gr4owzhr08bcwx5y", "doc_id": 220918572, "children": []}, {"title": "异地开户审核要求", "url": "nhgudcxxqm21ogwc", "doc_id": 220918345, "children": []}, {"title": "自助进件审核要求", "url": "qic8ff8a7bhiv08q", "doc_id": 220918298, "children": []}]}, {"title": "佣金结算", "url": "", "doc_id": "", "children": [{"title": "客服查每周佣金打款名单", "url": "xgigve31mqdsr6tb", "doc_id": 228289682, "children": []}, {"title": "中僖个体工商户佣金结算流程", "url": "lnmglp09e07izzxc", "doc_id": 222358105, "children": []}, {"title": "T1代理商佣金申请及增加发票额度流程", "url": "sw9bva7677cnk6g6", "doc_id": 221591588, "children": []}, {"title": "灵活用工代理商结算流程", "url": "gp4o0o48mf6lxkf3", "doc_id": 221590946, "children": []}, {"title": "受理商结算流程", "url": "sz7a9i153uv51fud", "doc_id": 220919221, "children": [{"title": "受理商T1结算", "url": "xwyo6if0xkmydh5g", "doc_id": 228255120, "children": []}, {"title": "受理商佣金结算FAQ", "url": "gz7514rc8808iky1", "doc_id": 221281547, "children": []}]}, {"title": "付呗代理商佣金结算流程", "url": "oyftiw2wkon7q76q", "doc_id": 219357546, "children": [{"title": "佣金报错QA", "url": "eg2pwzcvguorxube", "doc_id": 225598843, "children": [{"title": "Q：佣金申请实际应结佣金少于3,000元,无法提交", "url": "zy7v909mcwyygono", "doc_id": 225598894, "children": []}]}, {"title": "代理商佣金FAQ", "url": "ramnwqu0cmxlgb46", "doc_id": 221281832, "children": []}]}]}, {"title": "总后台", "url": "wbff6lx1w2um6xw4", "doc_id": 221129219, "children": []}, {"title": "账户能力", "url": "", "doc_id": "", "children": [{"title": "客服查询账户能力", "url": "iiaqrr6l796bgu69", "doc_id": 227909754, "children": []}, {"title": "银盛D0", "url": "ifue1zq0v55i7osm", "doc_id": 229104876, "children": []}, {"title": "预授权", "url": "ga7q2uzq42gkp52d", "doc_id": 225590529, "children": [{"title": "哪些设备支持预授权功能，怎么开通", "url": "kgkxp2cit5n6v0b4", "doc_id": 226637410, "children": []}]}, {"title": "乐刷笔笔秒到/乐刷S0", "url": "vzflhf2k8zagl1om", "doc_id": 226969410, "children": [{"title": "乐刷扫码S0", "url": "ekgmqx7nglocfw8r", "doc_id": 226966939, "children": []}, {"title": "乐刷刷卡S0", "url": "dqk7n50k00ta97l3", "doc_id": 226964965, "children": []}]}, {"title": "头寸退款", "url": "dowenzrz75kh6dga", "doc_id": 225989987, "children": []}, {"title": "分时结算", "url": "trmp3d2nh3eza2ni", "doc_id": 225989315, "children": [{"title": "开通流程", "url": "gyeawtklgago2kec", "doc_id": 230000678, "children": []}, {"title": "到账时间", "url": "whrgzwsvxgul5das", "doc_id": 230002143, "children": []}, {"title": "关闭修改流程", "url": "fnn9p0px3etpirrh", "doc_id": 230016064, "children": []}]}, {"title": "门店独立结算（现在不支持申请了 存量不影响）", "url": "an5r9c0h0afamv4m", "doc_id": 225621238, "children": [{"title": "门店独立结算FAQ", "url": "zm7bq0guwq7btocg", "doc_id": 225623566, "children": []}]}, {"title": "易生D0", "url": "gttywtk498mq1rlz", "doc_id": 226431884, "children": [{"title": "客服查看开通易生D0流程", "url": "la1h4ciquov3tldf", "doc_id": 227959053, "children": []}, {"title": "易生D0支付FAQ", "url": "vl30w4vz5pzfmwhh", "doc_id": 226962894, "children": []}]}, {"title": "乐刷提现立即到账", "url": "iimid81bidw8fry6", "doc_id": 221444088, "children": [{"title": "提现立即到账FAQ", "url": "dtrd5biz3565z6y7", "doc_id": 225974812, "children": []}]}, {"title": "乐刷分账", "url": "ow30p75f5hc8ffgk", "doc_id": 221222509, "children": [{"title": "乐刷比例分账", "url": "oqtqgs26c9rdhqb8", "doc_id": 225299026, "children": [{"title": "比例分账FAQ", "url": "gxakz6tl6bg19bk5", "doc_id": 225994440, "children": []}]}, {"title": "乐刷余额分账", "url": "fq9lui266ndbphvq", "doc_id": 224243346, "children": [{"title": "余额分账FAQ", "url": "ml4xv4pku22674gs", "doc_id": 225996964, "children": []}]}, {"title": "申请乐刷分账FAQ", "url": "fowzbd6hi7dvhbxk", "doc_id": 221397056, "children": []}]}]}, {"title": "登记流程", "url": "fagug8mgo8h0ana7", "doc_id": 226488442, "children": [{"title": "代付通道切换流程", "url": "pvxt2vb3qq0xyvao", "doc_id": 226916556, "children": []}, {"title": "商户入驻登记", "url": "brbtgx76hlgro0ga", "doc_id": 226488864, "children": []}, {"title": "商户代理商问题收集", "url": "kmla095rigwzgg7l", "doc_id": 227786999, "children": []}]}, {"title": "辅助文档", "url": "etfxozcvb6r86uu3", "doc_id": 226127264, "children": [{"title": "门店二维码解码", "url": "mwfy0qoa8rgxg6xs", "doc_id": 226148800, "children": []}, {"title": "碰一下收银员赏金查询", "url": "pro4b4cmh0rz44gx", "doc_id": 226134358, "children": []}, {"title": "银行卡校验", "url": "kn12bbdgme9gbxy6", "doc_id": 226129640, "children": []}, {"title": "联行号查询", "url": "czgiyiehs6ks9nlw", "doc_id": 226127426, "children": []}]}, {"title": "代理商钉钉流程SOP", "url": "", "doc_id": "", "children": [{"title": "代理商权限申请 ", "url": "lu7saybbq2ky8gqa", "doc_id": 219087248, "children": []}, {"title": "代理商佣金申请 ", "url": "gdzpb6uplp4me34x", "doc_id": 219087240, "children": []}, {"title": "代理商主体变更 ", "url": "xp66avqgv5v2at85", "doc_id": 219087199, "children": []}, {"title": "代理商账号基础信息变更 ", "url": "saxynbx703b93tsc", "doc_id": 219087186, "children": []}, {"title": "刷脸设备内部迁移申请 ", "url": "eo5aueyt651udb97", "doc_id": 219087179, "children": []}, {"title": "刷脸如意维保设备入库升级 ", "url": "kv0g32yi64oqfvkd", "doc_id": 219087169, "children": []}, {"title": "【校园】支付宝收款限制集门店分账 ", "url": "eo7i5fnz2mqw412n", "doc_id": 219087158, "children": []}, {"title": "微信高校机器配置【盛思达】 ", "url": "om8gwrqgcct37fpp", "doc_id": 219087146, "children": []}, {"title": "秋收&丰收计划达标退押金 ", "url": "lxgiduq1heergxgk", "doc_id": 219087137, "children": []}, {"title": "代理商系统软件采购流程 ", "url": "lnurh1e8p2hmqzs3", "doc_id": 219087132, "children": []}, {"title": "预授权功能申请 ", "url": "vrv6xg2r6g4trk83", "doc_id": 219087126, "children": []}, {"title": "商户提额申请 ", "url": "qpepgp5ozw5yeowt", "doc_id": 219087112, "children": [{"title": "商户提额资料要求", "url": "honoxuh98bbogzvo", "doc_id": 221450624, "children": []}]}, {"title": "入网次数超限、商户分店上报次数报备 ", "url": "gongnkng3lgk0rf8", "doc_id": 219087104, "children": [{"title": "入网次数资料要求", "url": "hyyeoqrzzeysbzbc", "doc_id": 221431874, "children": []}]}, {"title": "乐刷直连账户能力申请 ", "url": "ob666<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "doc_id": 219087100, "children": [{"title": "乐刷账户能力查询入口，开通状态", "url": "iw80xpdzoslq7haw", "doc_id": 221437122, "children": []}]}, {"title": "快速到账（S0）开通申请 ", "url": "tw5iyipe339p2b7r", "doc_id": 219087092, "children": []}, {"title": "【秋收计划】设备强制回收 ", "url": "kcif2uwl23ggbog1", "doc_id": 219087088, "children": []}, {"title": "代理商合作主体变更（营业执照名称变更申请） ", "url": "uw3y9hltvckzugwc", "doc_id": 219087079, "children": []}, {"title": "商户手续费开票流程 ", "url": "iogevkigmll6vvrt", "doc_id": 219087065, "children": []}, {"title": "微信高校设备退回 ", "url": "ndhke0bhwm1wag0p", "doc_id": 219087050, "children": []}, {"title": "集团账户申请 ", "url": "hu5vs719kt3hl31v", "doc_id": 219087045, "children": []}, {"title": "POS分期-提前还款流程 ", "url": "ge766a4k7p0xu4ir", "doc_id": 219087040, "children": []}, {"title": "提现立即到账申请 ", "url": "zrbx87ql6409antl", "doc_id": 219087035, "children": []}, {"title": "设备外采入库 ", "url": "xkl8mrkv5e3u4p5o", "doc_id": 219087019, "children": []}, {"title": "商户账户、手机号修改 ", "url": "shwko2or6gm3ntpt", "doc_id": 219087012, "children": []}, {"title": "终端POS转网流程 ", "url": "zvdo6z76k3ibxegg", "doc_id": 219087010, "children": []}, {"title": "误关商户 ", "url": "ft9e8cp20vsw4uyc", "doc_id": 219087003, "children": []}, {"title": "集团总部抽佣比例设置 ", "url": "qkxniaq8nyxc0gf4", "doc_id": 219086995, "children": []}, {"title": "代理商系统软件换绑流程 ", "url": "zqs1potqe35ib127", "doc_id": 219086988, "children": []}, {"title": "企业团餐政策报名&设备发货 ", "url": "gcpr6imeug5g490n", "doc_id": 219086981, "children": []}, {"title": "终端硬件转出申请 ", "url": "gnwxyggb6nqnuqtg", "doc_id": 219086398, "children": []}, {"title": "支付宝团购券签约上架流程 ", "url": "mfd51gnn8c9prssu", "doc_id": 219086396, "children": []}, {"title": "开票流程 ", "url": "sf4mamsc8ve6gmxp", "doc_id": 219086393, "children": []}, {"title": "富友特殊商户进件申请 ", "url": "gwy1d66wr9px63yv", "doc_id": 219086381, "children": []}, {"title": "富有通道商户银行卡修改 ", "url": "erb4rvkaaxd05yhx", "doc_id": 219086341, "children": []}, {"title": "其他通道分时开通 ", "url": "xyut67exit5df26a", "doc_id": 219086337, "children": []}, {"title": "终端增机流程 ", "url": "fg7i72i2fu9wybxt", "doc_id": 219085763, "children": []}, {"title": "非法人结算变更 ", "url": "zrsfg4rf8rrmthpt", "doc_id": 219086074, "children": []}, {"title": "NFC业务员账户 ", "url": "ku31vp9nd4o4up4y", "doc_id": 219086069, "children": []}, {"title": "跨区域开户申请 ", "url": "uggrfslevdkgl6ug", "doc_id": 219086061, "children": []}, {"title": "付呗商户迁移 ", "url": "gn0kpbwalp114kvm", "doc_id": 219086056, "children": []}, {"title": "冲电狼代理商账号申请 ", "url": "gzh9dardqqndvgfr", "doc_id": 219086054, "children": []}, {"title": "支付宝区代N7设备入库申请流程 ", "url": "bklg2smx8m3snpqh", "doc_id": 219086053, "children": []}, {"title": "商户电子发票申请 ", "url": "melv2om2f5gke453", "doc_id": 219088656, "children": []}, {"title": "花呗分期免息活动配置申请 ", "url": "vxfodonvkezmyf3u", "doc_id": 219088665, "children": []}, {"title": "外卡申请流程 ", "url": "rq3eils3l60uums5", "doc_id": 219088672, "children": []}]}, {"title": "来逛呗", "url": "nlqn81u4wlz750zo", "doc_id": 219222927, "children": [{"title": "目录", "url": "sulxpdne638miq2o", "doc_id": 219222982, "children": []}, {"title": "商圈业务", "url": "mgp5ge5qw5f71cez", "doc_id": 219230140, "children": [{"title": "来逛呗.运营后台", "url": "qwvfmtnv2u1daz9a", "doc_id": 219508797, "children": [{"title": "账户管理", "url": "zgrqi2r1a6qby4yo", "doc_id": 219556317, "children": [{"title": "收款账户", "url": "mrodfzsn0prog0xs", "doc_id": 220457671, "children": []}, {"title": "提现记录", "url": "xz7xikn6hf2bxy4o", "doc_id": 219591435, "children": []}]}, {"title": "集团管理", "url": "zn48qpdkba31ld6c", "doc_id": 221011356, "children": [{"title": "集团配置", "url": "nurq6s7e912l79of", "doc_id": 221011424, "children": []}, {"title": "集团列表", "url": "hcotfpy5ixwats8g", "doc_id": 221011402, "children": []}]}, {"title": "财务对账", "url": "tdygucia30ilvbef", "doc_id": 221011551, "children": [{"title": "集团对账", "url": "lgag74ly8h3qgs3m", "doc_id": 221011751, "children": []}, {"title": "门店对账", "url": "vot3a2p0wskd0fxm", "doc_id": 221011665, "children": []}]}, {"title": "订单管理", "url": "yyyhrcdh5x3uek0a", "doc_id": 225634818, "children": [{"title": "商品订单", "url": "za80gzrghbgoc405", "doc_id": 221011510, "children": []}]}]}, {"title": "来逛呗.商圈营销后台", "url": "er3phdqyif602kfa", "doc_id": 219509175, "children": [{"title": "数据中心", "url": "ag20uflhaqtdweme", "doc_id": 222573953, "children": [{"title": "抖音直播数据", "url": "ggv0wgx4wzy3e2s3", "doc_id": 222570220, "children": []}]}, {"title": "平台团购", "url": "pgpq0i95v1tbxkmy", "doc_id": 222726480, "children": [{"title": "商品发布", "url": "dhol3eex93g0ahds", "doc_id": 222726563, "children": []}, {"title": "订单概览", "url": "th5tksvx4l4s8prm", "doc_id": 223105370, "children": []}, {"title": "商品订单", "url": "gulzawd6tt38lwbi", "doc_id": 223117518, "children": []}, {"title": "核销明细", "url": "npgk9zda5oo9e182", "doc_id": 223119829, "children": []}]}, {"title": "联合营销卡", "url": "togwx2pk73eir12f", "doc_id": 223193230, "children": [{"title": "商圈卡管理", "url": "ubf2uu2rvzndefu1", "doc_id": 223193287, "children": []}, {"title": "商家券管理", "url": "htotg146bg88c7b0", "doc_id": 223210803, "children": []}, {"title": "商圈卡订单", "url": "xyh14bsb0heabcn8", "doc_id": 223223994, "children": []}, {"title": "商圈卡核销明细", "url": "bs16481d3vihsqsh", "doc_id": 223223986, "children": []}]}, {"title": "营销管理", "url": "siibt6oegiyqu1gf", "doc_id": 223239386, "children": [{"title": "营销活动", "url": "rqpx8nxv3o9l9mxl", "doc_id": 223239508, "children": []}]}, {"title": "组织管理", "url": "zczwcbyc6kwiuvqz", "doc_id": 223244733, "children": [{"title": "广场管理", "url": "zx2rrepda2hofzab", "doc_id": 223244912, "children": []}, {"title": "门店管理", "url": "tct1yqvfyz4mahkx", "doc_id": 223245110, "children": []}]}, {"title": "员工管理", "url": "gp4y4yw0sdpcleqv", "doc_id": 223253759, "children": [{"title": "员工管理", "url": "hhvg1hdx4pr6vhva", "doc_id": 223253988, "children": []}, {"title": "角色管理", "url": "yhzgcqhq7wun6gsu", "doc_id": 223254059, "children": []}]}]}, {"title": "来逛呗.商圈卡", "url": "dqr24m66impt61n2", "doc_id": 220388816, "children": [{"title": "价值理念", "url": "enwhly2xlswieelo", "doc_id": 221733078, "children": []}, {"title": "新拓商务对接", "url": "tlixv1utvznk4xi1", "doc_id": 220471222, "children": [{"title": "服务商对接SOP", "url": "cgyy983bl59uydst", "doc_id": 221269122, "children": []}, {"title": "直营商务对接SOP", "url": "ffx6ug3t8xfwymnq", "doc_id": 220404359, "children": []}]}, {"title": "新拓后台配置", "url": "mtdm0t1gzqa2c3di", "doc_id": 220471252, "children": [{"title": "服务商后台配置SOP", "url": "xsko86u9su6v6no1", "doc_id": 221713429, "children": []}, {"title": "直营后台配置SOP", "url": "woirrwfp13co9f8s", "doc_id": 220404515, "children": []}]}, {"title": "商圈卡交付", "url": "ctwz0oubx5688rbx", "doc_id": 221719478, "children": [{"title": "交付注意事项", "url": "xv3ga5h1np531d1t", "doc_id": 225350058, "children": []}, {"title": "参活商户信息录入", "url": "hw00ukmzz29vcwgk", "doc_id": 221727242, "children": []}, {"title": "AI商家券（子券）创建SOP", "url": "dietux8yrwnq6f9h", "doc_id": 220406356, "children": []}, {"title": "来逛呗物料尺寸", "url": "qiv51rb7u6iz0a3u", "doc_id": 220406442, "children": []}, {"title": "商圈卡（母券）创建", "url": "go3ku3aogzmqn8yw", "doc_id": 221735647, "children": []}, {"title": "资金配置检核SOP", "url": "kbg5p7kqdvf0q1kc", "doc_id": 221720041, "children": []}]}, {"title": "商圈卡运营", "url": "tewaolu1r0ro1tsg", "doc_id": 221730100, "children": [{"title": "用户（C端）激活流程说明", "url": "sfk67hvgtg4nqfsg", "doc_id": 221733479, "children": []}, {"title": "赔偿机制", "url": "uh57ei5ge616xz6m", "doc_id": 221733161, "children": []}, {"title": "建券注意事项", "url": "kczdnaf0m2kwxgwf", "doc_id": 221733310, "children": []}, {"title": "评价运营管理", "url": "okk2ktfdqhr6x6w6", "doc_id": 221730535, "children": []}]}]}, {"title": "来逛呗.商家版", "url": "xrnvdwve1gw9ykfi", "doc_id": 222023745, "children": [{"title": "登录方式", "url": "qcdnryaeqe35cy7e", "doc_id": 222023864, "children": [{"title": "手机验证码登录", "url": "lb8bgny9d4vbfry2", "doc_id": 222387679, "children": []}, {"title": "微信号授权登录", "url": "mefxc8pdd2cdockg", "doc_id": 222387570, "children": []}]}, {"title": "商家核券", "url": "bb8sh9srz2l1ff8x", "doc_id": 222024057, "children": [{"title": "输码核券", "url": "ixyermng0umg3l6i", "doc_id": 222229114, "children": []}, {"title": "扫码核券", "url": "rzatzqz972cy4bup", "doc_id": 222229100, "children": []}]}, {"title": "商家对账", "url": "rehnfto0n9nr64fn", "doc_id": 222024065, "children": [{"title": "核销明细", "url": "adb6su8r9nfzc87g", "doc_id": 222388896, "children": []}, {"title": "财务对账", "url": "lrozazc69zvgk3n6", "doc_id": 222388869, "children": []}]}, {"title": "账户余额", "url": "ev9bktsw0medfatv", "doc_id": 222024076, "children": [{"title": "提现记录", "url": "nubtrcgh6r7a57a5", "doc_id": 223905232, "children": []}, {"title": "账户信息绑卡", "url": "e<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "doc_id": 223905222, "children": []}]}, {"title": "员工管理", "url": "ear7x0ove476logx", "doc_id": 222024115, "children": [{"title": "角色管理", "url": "hn9wmqg3kf3q4adn", "doc_id": 223920099, "children": []}, {"title": "员工管理", "url": "toeb66e48u3qe7n6", "doc_id": 223920078, "children": []}]}, {"title": "其他功能", "url": "vp6ml72rs5ynx9zk", "doc_id": 222024128, "children": [{"title": "帮助中心", "url": "ppf4eigx4falzbqs", "doc_id": 223921764, "children": []}, {"title": "商户手续费发票申请", "url": "hkm13i424b9g5nlp", "doc_id": 223921747, "children": []}, {"title": "客服入口", "url": "yk7e4azvis4hprv1", "doc_id": 223921723, "children": []}]}]}, {"title": "来逛呗(用户端)", "url": "dueooqxiqgcwcdil", "doc_id": 223253472, "children": [{"title": "使用说明", "url": "gv9ventspbfte0sx", "doc_id": 224683654, "children": [{"title": "登入方式", "url": "mbaw8i57gx9kgr5g", "doc_id": 224683850, "children": []}, {"title": "操作流程", "url": "ncza3mcixd0rttpn", "doc_id": 224683820, "children": []}, {"title": "售后问题", "url": "egi2d5vhabhcx8q4", "doc_id": 224683796, "children": []}, {"title": "开票说明", "url": "qmg6k8sbsxggyc36", "doc_id": 224861672, "children": []}]}]}, {"title": "万达小程序", "url": "wpwyml1qu8ytlfpg", "doc_id": 223253174, "children": [{"title": "基本操作", "url": "sxhs6s479o8ck4ir", "doc_id": 224882396, "children": [{"title": "商户入驻", "url": "cczb89x1ok0112um", "doc_id": 224888918, "children": []}, {"title": "余额提现", "url": "wnuskwohhp7r7q5u", "doc_id": 225290832, "children": []}, {"title": "结算账户修改", "url": "fmdz5uws8nk4so7s", "doc_id": 225311082, "children": []}]}]}, {"title": "来逛呗.其他事项", "url": "wysgqigxbii4mk2d", "doc_id": 221863792, "children": [{"title": "服务商对接sop", "url": "yyga4ih8f0nxcths", "doc_id": 221864811, "children": []}, {"title": "抖音营销佣金申请流程", "url": "yelctrzixgvulzpf", "doc_id": 221865923, "children": []}, {"title": "抖音营销佣金申请打款进度查询", "url": "czggexgb39n2hn2u", "doc_id": 222026749, "children": []}, {"title": "商圈卡佣金申请流程", "url": "arw3awvl6g0o1ste", "doc_id": 222141504, "children": []}, {"title": "商圈卡佣金申请打款进度查询", "url": "ivqichr3ezavehdq", "doc_id": 222144369, "children": []}, {"title": "商户手续费发票申请处理流程", "url": "lkw96e6top4rs2li", "doc_id": 222147104, "children": []}]}, {"title": "来逛呗.违规整改", "url": "eetwkhem9rzn6ang", "doc_id": 223397660, "children": [{"title": "广场违规整改处理", "url": "nomrd2nee1g3lg66", "doc_id": 223406718, "children": [{"title": "概览", "url": "wq0ib0v4x8n9eg0k", "doc_id": 223398163, "children": []}, {"title": "风险诊断", "url": "fqiv00eqglr0rlek", "doc_id": 223403312, "children": []}, {"title": "预警记录", "url": "ci6fk60mqtp5n7kl", "doc_id": 223403391, "children": []}, {"title": "违规记录", "url": "fm57p92v8fighrgc", "doc_id": 223403542, "children": []}, {"title": "赔付记录", "url": "uw7f3qrdbmbwka97", "doc_id": 223403619, "children": []}]}, {"title": "共性问题登记表", "url": "gf4vyk4s2ncuzg4c", "doc_id": 223407198, "children": []}]}, {"title": "来逛呗.常见问题QA", "url": "owxcso5rax4m97oa", "doc_id": 222400380, "children": []}]}, {"title": "商圈钉钉审批流程", "url": "wggcctsa7hkfgq8c", "doc_id": 220389016, "children": [{"title": "来逛呗架构-商圈卡创建", "url": "ggtn5l3axn88elwq", "doc_id": 221550706, "children": []}, {"title": "来逛呗架构-商圈卡核查", "url": "zykb0gon2srn073g", "doc_id": 221443183, "children": []}, {"title": "来逛呗架构-新拓商圈后台配置", "url": "ole2wy64fxdgmmal", "doc_id": 221407140, "children": []}, {"title": "来逛呗架构-商圈卡信息修改&订正", "url": "kkstg59kxkgyte6v", "doc_id": 221442347, "children": []}, {"title": "来逛呗架构-服务商商圈卡佣金申请", "url": "awea1nrriv81cezd", "doc_id": 221439882, "children": []}, {"title": "首展架构-新拓商圈后台配置", "url": "qimg23xe8b6gion2", "doc_id": 220399873, "children": []}, {"title": "首展架构-商圈卡创券", "url": "xkgt3fgsp1i3duro", "doc_id": 221675259, "children": []}, {"title": "首展架构-商圈卡信息订正", "url": "lbyygrz4c3k7m4yx", "doc_id": 220390503, "children": []}, {"title": "首展架构-商圈数据订正", "url": "notq3cct10tlenbg", "doc_id": 221681849, "children": []}, {"title": "首展架构-商圈外部资金出款", "url": "yt75t8mkkwhetc7i", "doc_id": 220389056, "children": []}, {"title": "首展架构-商圈卡修改", "url": "ief0u49zqa3ggbha", "doc_id": 221679491, "children": []}, {"title": "首展架构-商圈出款", "url": "gwutwrveeom1luit", "doc_id": 221707190, "children": []}, {"title": "首展架构-来逛呗到单审批", "url": "rwtlto5r1wq8whxl", "doc_id": 222306452, "children": []}]}, {"title": "本地生活业务", "url": "uxey6c44gk33lklw", "doc_id": 219230177, "children": [{"title": "林客", "url": "nvbux2g42ym5wc4w", "doc_id": 224253926, "children": [{"title": "登录通道", "url": "ty0hssaxg4xomxuc", "doc_id": 224254169, "children": []}, {"title": "合作管理", "url": "rrfqi56g3odennfw", "doc_id": 224254154, "children": [{"title": "订单管理", "url": "vklr56upu11g4thl", "doc_id": 224255704, "children": []}, {"title": "取消订单管理", "url": "mad1e46ih6h69bgl", "doc_id": 224255751, "children": []}]}, {"title": "商家服务", "url": "ph8e9am1kdng7cth", "doc_id": 224368834, "children": [{"title": "合作商家", "url": "uquagy2bi0ndgqol", "doc_id": 224368898, "children": []}]}, {"title": "生成有数", "url": "tyktqwswe160zg0g", "doc_id": 224369911, "children": [{"title": "券码数据", "url": "bkq8bmi7pzflhv4k", "doc_id": 224369975, "children": []}]}]}, {"title": "佣金系统", "url": "acha0vupe7k099pa", "doc_id": 222244734, "children": [{"title": "新增商户", "url": "zw70nue8eigekd4m", "doc_id": 223366670, "children": []}, {"title": "账户开通", "url": "qg2gwna9skadvgl6", "doc_id": 223379843, "children": [{"title": "服务商后台进件", "url": "lf0c7l65sukhy6ih", "doc_id": 223379983, "children": []}, {"title": "富友进件", "url": "oaaron7lg5quvqvn", "doc_id": 223379980, "children": []}, {"title": "富友线下资料提交", "url": "hivk33h6qrugll5c", "doc_id": 223404148, "children": []}, {"title": "商户签署协议", "url": "kvu200ihpnrhxktw", "doc_id": 223379974, "children": []}, {"title": "资金验证", "url": "dqh77y2kv7x79dl5", "doc_id": 223383549, "children": []}, {"title": "设置渠道佣金", "url": "ueg8cgpf0oww1ptn", "doc_id": 223384084, "children": []}]}]}, {"title": "智荐惊喜前置说明", "url": "hm9kwqlpms9g45ag", "doc_id": 222387425, "children": []}, {"title": "智荐惊喜（运营端）", "url": "xqdlg2a60p8cupqg", "doc_id": 222413004, "children": [{"title": "商家管理", "url": "fvbulhs75fk5xu3u", "doc_id": 222414604, "children": [{"title": "意向商家", "url": "nc2kydo8ydnnsx7v", "doc_id": 222415115, "children": []}, {"title": "合作商家", "url": "zwn4ausaydscyytg", "doc_id": 222415108, "children": []}, {"title": "商家开票", "url": "mehrudzzgggcl94e", "doc_id": 222415086, "children": []}]}, {"title": "服务管理", "url": "opaybdpzg59rqlv2", "doc_id": 222414595, "children": [{"title": "服务签约", "url": "dk6g68rg6t8ge306", "doc_id": 222444889, "children": []}, {"title": "服务订单", "url": "nkoib2coxndfkh3s", "doc_id": 222444887, "children": []}, {"title": "定向发单", "url": "yriz52lmwss4imxg", "doc_id": 222444875, "children": []}]}, {"title": "达人管理", "url": "qg7fipr41vl47zyn", "doc_id": 222414588, "children": [{"title": "达人计划", "url": "gbryz8vvkaneog73", "doc_id": 222549384, "children": []}]}, {"title": "服务商管理", "url": "nhgp1adppeesg6n5", "doc_id": 222414541, "children": [{"title": "服务商列表", "url": "szftd4cidehytqse", "doc_id": 222551928, "children": []}, {"title": "来团呗账套", "url": "wzby64nctsk5szyd", "doc_id": 222551918, "children": []}]}, {"title": "佣金审核", "url": "vhxizaq783q7ellg", "doc_id": 222414524, "children": [{"title": "审核申请", "url": "kh7p2zrcqufvwv5w", "doc_id": 222938324, "children": []}, {"title": "结算对账", "url": "impp7lz3bp4gsf1k", "doc_id": 222938314, "children": []}]}]}, {"title": "智荐惊喜（服务商网页端）", "url": "pz0rz2o1nma4ma4v", "doc_id": 222413013, "children": [{"title": "商家管理", "url": "lmxgz25g6twgcgt5", "doc_id": 222941849, "children": [{"title": "意向商家", "url": "npvfw2enbugr3vk4", "doc_id": 222942101, "children": []}, {"title": "合作商家", "url": "tz98e6gvgf1a3ku7", "doc_id": 222942096, "children": []}, {"title": "来团呗账套", "url": "dr0onpodq6f5l2vq", "doc_id": 222942088, "children": []}]}, {"title": "服务管理", "url": "cbg4ugpnhtvxvgg7", "doc_id": 222941887, "children": [{"title": "服务签约", "url": "quhdkblh6wz8stx9", "doc_id": 223205766, "children": []}, {"title": "服务订单", "url": "daoyzbscei2pws6y", "doc_id": 223205751, "children": []}]}, {"title": "达人管理", "url": "dqq6gnhyonlse3bv", "doc_id": 222941899, "children": [{"title": "达人列表", "url": "wqh32kcaqiqg6u7p", "doc_id": 223242315, "children": []}, {"title": "达人计划", "url": "soaq430qdd01t1gu", "doc_id": 223242291, "children": []}]}, {"title": "市场经理管理", "url": "vsucc1271qg5znrl", "doc_id": 222941912, "children": [{"title": "市场经理列表", "url": "ogeml2mgm9a1vzpg", "doc_id": 223245796, "children": []}]}, {"title": "经营数据", "url": "fbg1nl8ypuw2q0mi", "doc_id": 222941937, "children": [{"title": "核销数据", "url": "lknwygtu52akyiwb", "doc_id": 223248105, "children": []}, {"title": "商家数据", "url": "bkk29ndzxz5f06vp", "doc_id": 223248099, "children": []}, {"title": "商品数据", "url": "ixthti8hmdg5x220", "doc_id": 223248091, "children": []}, {"title": "员工数据", "url": "mt87g082mhyi20cy", "doc_id": 223248083, "children": []}]}, {"title": "财务管理", "url": "qd5qlmxg9n0bim4y", "doc_id": 222941958, "children": [{"title": "佣金结算", "url": "eltsdqyf60tvnhi6", "doc_id": 223256243, "children": []}, {"title": "佣金结算（新）", "url": "oxphhhmghm9iezgc", "doc_id": 223256215, "children": []}]}, {"title": "智能剪辑", "url": "wfy78q74scrqt8q4", "doc_id": 222941970, "children": [{"title": "素材管理", "url": "wcsp1ek16bu8nx98", "doc_id": 223266420, "children": []}, {"title": "视频合成", "url": "egvoccyv42uz3g02", "doc_id": 223266537, "children": []}, {"title": "素材剪辑", "url": "ftzzwf7ymc8kccuc", "doc_id": 223266530, "children": []}]}, {"title": "标签管理", "url": "tmamqyixfoy9skto", "doc_id": 222941997, "children": [{"title": "标签列表", "url": "vkw2bqaayo28fgy1", "doc_id": 223268883, "children": []}]}, {"title": "子账号管理", "url": "bc8sy0qvg2vzv48y", "doc_id": 222942012, "children": [{"title": "员工列表", "url": "ilcyr6k4ng2azg96", "doc_id": 223271376, "children": []}, {"title": "角色权限", "url": "tfvr5lfow8k48gaa", "doc_id": 223271367, "children": []}]}]}, {"title": "智荐惊喜（服务商小程序端）", "url": "arw1zhoncdr5mc0v", "doc_id": 223275825, "children": [{"title": "智荐惊喜小程序登录流程", "url": "oprzxzhf4v5sgnea", "doc_id": 224410684, "children": []}, {"title": "商家", "url": "mfrp4nk6n3y3b8p6", "doc_id": 224411436, "children": [{"title": "意向商家", "url": "vck764zqorx1ltv2", "doc_id": 224411454, "children": []}, {"title": "合作商家", "url": "cadexvrngyr37gzs", "doc_id": 224411451, "children": []}]}, {"title": "服务", "url": "qsfsf8fzupau6nb3", "doc_id": 225003655, "children": [{"title": "服务签约", "url": "mgyrw957wiyum2c5", "doc_id": 225003936, "children": []}, {"title": "服务订单", "url": "zaeog4pqts9ky3tg", "doc_id": 225004004, "children": []}]}, {"title": "数据", "url": "lpnbl17svhprru8b", "doc_id": 225003641, "children": []}, {"title": "我的", "url": "qnsseplkytomcheh", "doc_id": 225003612, "children": [{"title": "达人矩阵", "url": "wyisu3al9rhhv7nk", "doc_id": 225027817, "children": []}]}]}, {"title": "快团佣市场经理", "url": "hkx5ut602zhaxzz0", "doc_id": 222413249, "children": [{"title": "商家管理", "url": "rw2qgag8e1vkgiim", "doc_id": 224032516, "children": [{"title": "意向商家", "url": "ctdl48qg1ksklv19", "doc_id": 224032601, "children": []}, {"title": "合作商家", "url": "exh6bogk3s29b057", "doc_id": 224032596, "children": []}, {"title": "来团呗账套", "url": "ym8s4vx9k20hq5fc", "doc_id": 224032589, "children": []}]}, {"title": "服务管理", "url": "egksbg48h59e90c3", "doc_id": 224032511, "children": [{"title": "服务签约", "url": "eqt2wqyrqhbrrybq", "doc_id": 224044172, "children": []}, {"title": "服务订单", "url": "ymb6dexuk2w7irgs", "doc_id": 224044140, "children": []}]}, {"title": "经营数据", "url": "pxo56tpq48tp8qlk", "doc_id": 224032506, "children": [{"title": "核销数据", "url": "eaghrfg995kwy7n1", "doc_id": 224184056, "children": []}, {"title": "商家数据", "url": "daxa1awi8c81l0lq", "doc_id": 224184054, "children": []}, {"title": "商品数据", "url": "hmipqn34qatlgpch", "doc_id": 224184051, "children": []}, {"title": "员工数据", "url": "dm0dydntbybylr5o", "doc_id": 224184044, "children": []}]}, {"title": "财务管理", "url": "pyitzoxl0vr1bc3k", "doc_id": 224032500, "children": [{"title": "佣金结算", "url": "mpvrlydp4sitmxgg", "doc_id": 224184582, "children": []}, {"title": "佣金结算（新）", "url": "hy0nr717ncbp12ig", "doc_id": 224184570, "children": []}]}, {"title": "子账号管理", "url": "wy31304a4owk8i3x", "doc_id": 224032476, "children": [{"title": "账号列表", "url": "cb8hs0oogbhexuth", "doc_id": 224227026, "children": []}, {"title": "角色权限", "url": "bnnrgk531grs49ah", "doc_id": 224226993, "children": []}]}]}, {"title": "来团呗（商家APP端）", "url": "tytbvd0g2dkw5ya7", "doc_id": 220588030, "children": [{"title": "来团呗账号开通", "url": "vldwk5i33cvebz24", "doc_id": 220747229, "children": []}, {"title": "首页", "url": "vc63imcdln4cqydq", "doc_id": 220748529, "children": [{"title": "门店经营", "url": "cgg8m2913c2nkkan", "doc_id": 220514484, "children": [{"title": "数据", "url": "pkavzh9drzp5asw0", "doc_id": 220746950, "children": []}, {"title": "扫码销券", "url": "gb2btwzp2t742leq", "doc_id": 220749385, "children": []}, {"title": "输码验券", "url": "dhguyq8gx3zfh9fl", "doc_id": 222248496, "children": []}, {"title": "商品管理", "url": "lbvio7v4wxzdnday", "doc_id": 220747059, "children": []}, {"title": "交易统计", "url": "gggvy3mzzaw7ld4i", "doc_id": 220748868, "children": []}, {"title": "开票管理", "url": "chg67le9ulni3ull", "doc_id": 220748913, "children": []}, {"title": "打印设置", "url": "xo7urkqirq6eney7", "doc_id": 220748941, "children": []}]}, {"title": "营销推广", "url": "oo9qda9gbnrstmh5", "doc_id": 220598653, "children": [{"title": "智能剪辑", "url": "kvz6gyrsdod0x9yr", "doc_id": 220781967, "children": []}, {"title": "门店素材", "url": "cqfkx58bue6u7gal", "doc_id": 220781957, "children": []}, {"title": "顾客推", "url": "sdp4am99dfscznmz", "doc_id": 220781942, "children": []}, {"title": "达人矩阵", "url": "pt8y4rkf7ofqy35h", "doc_id": 220781938, "children": []}, {"title": "达人推", "url": "zqu5uruaqd6bhdeg", "doc_id": 220781920, "children": []}, {"title": "客资宝", "url": "bcewi3uf5nrtwpwc", "doc_id": 220781914, "children": []}, {"title": "团小贝", "url": "ymogisqpggpogh70", "doc_id": 220781901, "children": []}, {"title": "智播通", "url": "aucia7884i4yuzkn", "doc_id": 220782117, "children": []}, {"title": "来探呗", "url": "alafifid7rzhem7i", "doc_id": 221462472, "children": []}]}]}, {"title": "直播", "url": "hzqnxdvevpy50cf3", "doc_id": 221463897, "children": []}, {"title": "订单", "url": "igzb39ei6qidd3r8", "doc_id": 224241472, "children": []}, {"title": "我的", "url": "wsr7mdv74aaq4gox", "doc_id": 221468688, "children": [{"title": "商户信息", "url": "hsboaehyy0g2prpk", "doc_id": 221468720, "children": []}, {"title": "门店管理", "url": "kxc4qxtu5i7pd8g2", "doc_id": 221468943, "children": []}, {"title": "员工管理", "url": "flawgfntkkg0d0hw", "doc_id": 221468962, "children": []}, {"title": "开票管理", "url": "diot7e2l0c7egwop", "doc_id": 221473890, "children": []}, {"title": "帮助中心", "url": "ow9v2xhtmlzsk8o7", "doc_id": 221468977, "children": []}, {"title": "联系客服", "url": "hl8g87mmza68w0xu", "doc_id": 221469019, "children": []}, {"title": "设置", "url": "hnb7ig1n7p6d3x5w", "doc_id": 221469043, "children": []}]}]}, {"title": "来团呗（商家网页端）", "url": "ugfypyfh7h81o6xg", "doc_id": 220092624, "children": [{"title": "首页", "url": "mhuc1y49sdn7iul5", "doc_id": 220098699, "children": []}, {"title": "门店管理", "url": "xuuue2vwpnw10qa0", "doc_id": 220099041, "children": []}, {"title": "创作中心", "url": "pwt8kslhckfdr5oq", "doc_id": 220099107, "children": [{"title": "门店素材", "url": "wl7cck63bgcwxeg5", "doc_id": 220099143, "children": []}, {"title": "智能剪辑", "url": "btscoetpm4g5vhtt", "doc_id": 220099267, "children": []}]}, {"title": "活动推广", "url": "ky5kddclo80477fv", "doc_id": 220098794, "children": [{"title": "顾客推", "url": "hr04b8x00msxfwpi", "doc_id": 220098843, "children": []}, {"title": "达人推", "url": "hfk5ctouk5nqgb8g", "doc_id": 220098881, "children": []}]}, {"title": "达人矩阵", "url": "yrtnyglg6yizg1wc", "doc_id": 221143491, "children": []}]}, {"title": "来探呗小程序端", "url": "lqsvn3az76dz5iu6", "doc_id": 223533682, "children": [{"title": "来探呗小程序使用前置说明", "url": "tkngygslxqbngwbo", "doc_id": 223534753, "children": []}, {"title": "来探呗小程序商家端", "url": "fe5hv9ppmwc065eg", "doc_id": 223533723, "children": [{"title": "管理", "url": "iv1y5qhndb38cony", "doc_id": 223537076, "children": []}, {"title": "达人", "url": "oq3rliqpyx7pys74", "doc_id": 223537070, "children": []}, {"title": "战队", "url": "dwgwostx04hlhnot", "doc_id": 223537067, "children": []}, {"title": "我的", "url": "wyoregps0gvmnq6g", "doc_id": 223537043, "children": []}]}, {"title": "来探呗小程序达人端", "url": "cks8pnb1rgx27hva", "doc_id": 223533742, "children": [{"title": "广场", "url": "hkbus8x3hbcv46y8", "doc_id": 223593848, "children": []}, {"title": "已报名", "url": "qfvk04ewr0pmx6vl", "doc_id": 223593846, "children": []}, {"title": "团队", "url": "trnqwv92wg6w7c68", "doc_id": 223593843, "children": []}, {"title": "我的", "url": "xvmt4bou6mnw0010", "doc_id": 223593837, "children": []}]}]}, {"title": "来探呗运营后台", "url": "swx3vat0quidzl0u", "doc_id": 221756276, "children": [{"title": "用户管理", "url": "fvhgpcuwnclmpr6w", "doc_id": 221756372, "children": [{"title": "商家管理", "url": "gfs2gphkpxd7qu11", "doc_id": 221756713, "children": []}, {"title": "达人管理", "url": "au9iydxkke7etfa1", "doc_id": 221756703, "children": []}]}, {"title": "任务管理", "url": "vyes30gsu3syowq4", "doc_id": 221756367, "children": [{"title": "任务列表", "url": "pkvtldmmr3bc1tqv", "doc_id": 221756786, "children": []}, {"title": "作品列表", "url": "tduudmtle54fnv21", "doc_id": 221756781, "children": []}]}, {"title": "资金管理", "url": "sdwr6kfa5d2s6igm", "doc_id": 221756343, "children": [{"title": "提现申请", "url": "isz19un25nagoeck", "doc_id": 221756850, "children": []}]}, {"title": "子账号管理", "url": "gsneh5arc22pyokl", "doc_id": 221756349, "children": [{"title": "账户列表", "url": "znrwe80adz4ur8ez", "doc_id": 221756866, "children": []}, {"title": "角色权限", "url": "ocp5d2tudtxowpph", "doc_id": 221756864, "children": []}]}]}]}, {"title": "代运营操作", "url": "rdoz5cqwx4v0a4kc", "doc_id": 220096224, "children": [{"title": "秒杀设置", "url": "iohw3t35txhuuwxx", "doc_id": 221214154, "children": [{"title": "线上店秒杀SOP", "url": "cg9kgnpw7kxre8g4", "doc_id": 220096325, "children": []}, {"title": "直播间秒杀SOP", "url": "vvq4xxklbq1l9tmg", "doc_id": 220237548, "children": []}, {"title": "编辑秒杀活动&查看秒杀数据SOP", "url": "bc72bztg5sy85kc7", "doc_id": 220937131, "children": []}]}, {"title": "福袋设置", "url": "rr76lvewq2ygrtv0", "doc_id": 221214396, "children": [{"title": "钻石福袋SOP", "url": "ekve8c08cgrmxe6h", "doc_id": 221225931, "children": []}, {"title": "优惠券福袋SOP", "url": "wbni1h0h5rzmvbnr", "doc_id": 220242285, "children": []}, {"title": "实物福袋SOP", "url": "saht36c0t0iyq58g", "doc_id": 221443922, "children": []}]}, {"title": "达人计划设置", "url": "kdsipkdi361qti0q", "doc_id": 221214528, "children": [{"title": "通用达人（全店推广）计划SOP", "url": "tctquf1b5oa7ptit", "doc_id": 220249284, "children": []}, {"title": "定向达人计划SOP", "url": "cxpraiesrnavd349", "doc_id": 220260594, "children": []}, {"title": "招募达人计划SOP", "url": "eurgxds0ow7r352g", "doc_id": 220271388, "children": []}]}, {"title": "商家粉丝券设置", "url": "hed0qzmgfdw8ufym", "doc_id": 221741969, "children": [{"title": "商家粉丝券SOP", "url": "mmmgkb1m0gz17bhd", "doc_id": 221720912, "children": []}]}]}]}]
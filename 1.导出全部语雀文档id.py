# -*- coding:utf-8-*-
import requests
import re
import json
from urllib.parse import unquote


def build_full_tree(docs_list_original):
    """
    构建包含所有原始字段的完整层级树。
    节点是原始文档的副本，并添加了 'children' 键。
    """
    doc_map = {}
    copied_docs_list = []
    for original_doc in docs_list_original:
        doc_copy = original_doc.copy()
        doc_copy['children'] = []
        doc_map[doc_copy['uuid']] = doc_copy
        copied_docs_list.append(doc_copy)

    tree_roots = []
    for doc_copy in copied_docs_list:
        parent_uuid = doc_copy.get('parent_uuid', '')
        if parent_uuid:
            parent_node_copy = doc_map.get(parent_uuid)
            if parent_node_copy:
                parent_node_copy['children'].append(doc_copy)
            else:
                tree_roots.append(doc_copy)
        else:
            tree_roots.append(doc_copy)

    for node_uuid in doc_map:
        parent_node_copy = doc_map[node_uuid]
        if parent_node_copy.get('child_uuid') and parent_node_copy['children']:
            ordered_children = []
            current_child_uuid = parent_node_copy['child_uuid']
            visited_in_chain = set()

            while current_child_uuid and current_child_uuid not in visited_in_chain:
                visited_in_chain.add(current_child_uuid)
                child_node_from_map = doc_map.get(current_child_uuid)

                if child_node_from_map:
                    ordered_children.append(child_node_from_map)
                    current_child_uuid = child_node_from_map.get('sibling_uuid', '')
                else:
                    break
            parent_node_copy['children'] = ordered_children
    return tree_roots


def _format_node_for_output_recursive(full_node):
    """
    递归地将完整节点格式化为仅包含 title, doc_id, children 的字典。
    """
    formatted_node = {
        'title': full_node['title'],
        'url': full_node['url'],
        'doc_id': full_node['doc_id']
    }

    if full_node.get('children'):
        formatted_node['children'] = [
            _format_node_for_output_recursive(child_node) for child_node in full_node['children']
        ]
    else:
        formatted_node['children'] = []

    return formatted_node


def generate_formatted_tree_output(original_docs_list):
    """
    生成最终的、仅包含指定字段的层级树结构。
    """
    full_hierarchical_roots = build_full_tree(original_docs_list)

    final_output_tree = []
    for root_full_node in full_hierarchical_roots:
        final_output_tree.append(_format_node_for_output_recursive(root_full_node))

    return final_output_tree


headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
}

# 获取语雀登录的cookie
cookies = {
    "_yuque_session": "wEBXOAr4uWYdSZnbD3UlbmzlyqbO3Wv7FpZCGkGcoBZxc-xr8j4VIcGi4tcATQhAw4LGzSScXDdawN-wS60REA==",
    "yuque_ctoken": "jiVZNzUYc-65F2Ec5q76Xxyv",
}

# 要采集的文档主页
url = "https://fshows.yuque.com/tech-ozd0u/szv77s"
response = requests.get(url, headers=headers, cookies=cookies)

data = re.search(r'decodeURIComponent\((.*?)\)\);', response.text, re.S).group(1)
data_dict = json.loads(unquote(eval(data)))

documents_list = data_dict['book']['toc']
print(documents_list)

final_json_data = generate_formatted_tree_output(documents_list)
json_output_str = json.dumps(final_json_data, ensure_ascii=False)

with open('yuque_doc_id.json', 'w', encoding='utf-8') as f:
    f.write(json_output_str)

# -*- coding:utf-8-*-
import json
import os
import re
import requests
import time
from concurrent.futures import ThreadPoolExecutor

"""
下载有目录结构的文件到本地，yuque_documents
"""

# 获取语雀登录的cookie
yuque_ctoken = 'jiVZNzUYc-65F2Ec5q76Xxyv'
_yuque_session = 'wEBXOAr4uWYdSZnbD3UlbmzlyqbO3Wv7FpZCGkGcoBZxc-xr8j4VIcGi4tcATQhAw4LGzSScXDdawN-wS60REA=='

INPUT_JSON_FILE = "yuque_doc_id.json"  # 读取的文件名称
BASE_DOWNLOAD_DIR = "yuque_documents"  # 保存文件夹名称
MAX_WORKERS = 8  # 最大并发下载线程数


def download_yuque_markdown_content(doc_url, doc_id):
    """
    获取单个文档的下载链接和类型
    """
    headers = {
        "Content-Type": "application/json",
        "x-csrf-token": yuque_ctoken,
    }
    cookies = {
        "_yuque_session": _yuque_session,
        "yuque_ctoken": yuque_ctoken,
    }
    export_api_url = f'https://fshows.yuque.com/api/docs/{doc_url}?book_id=64099364'  # 这里的book_id可能是固定的或者需要从JSON中读取

    try:
        response1 = requests.get(export_api_url, headers=headers, cookies=cookies, timeout=15).json()
    except Exception as e:
        print(f"  网络错误获取文档信息 {doc_url} ({doc_id}): {e}")
        return None, None

    doc_type = response1.get('data', {}).get('type')
    if not doc_type:
        print(f"  无法确定文档类型或文档不存在: {doc_url} ({doc_id})")
        return None, None

    if doc_type == 'Sheet':
        export_data = {
            "type": "excel",
            "force": 0
        }
    else:
        export_data = {
            "type": "markdown",
            "force": 0,
            "options": "{\"latexType\":1}"
        }

    export_trigger_url = f"https://fshows.yuque.com/api/docs/{doc_id}/export"
    export_data_json = json.dumps(export_data, separators=(',', ':'))

    # 尝试触发导出，最多重试5次
    for _ in range(5):
        try:
            export_response = requests.post(export_trigger_url, headers=headers, cookies=cookies, data=export_data_json,
                                            timeout=15).json()
        except Exception as e:
            print(f"  解析错误触发导出 {doc_id}: {e}, 稍后重试...")
            time.sleep(2)
            continue

        message = export_response.get('message')
        if message == '请发布后再导出':
            print(f"  文档未发布，无法导出: {doc_url} ({doc_id})")
            return None, None

        doc_link = export_response.get('data', {}).get('url')

        if doc_link:
            if doc_type == 'Sheet' and not doc_link.startswith('http'):
                doc_link = 'https://fshows.yuque.com' + doc_link

            try:
                download_response = requests.get(doc_link, headers=headers, cookies=cookies, timeout=30, stream=True)
                download_response.raise_for_status()
                return download_response, doc_type
            except requests.exceptions.RequestException as e:
                print(f"  下载文件失败 {doc_id} ({doc_link}): {e}, 稍后重试...")
                time.sleep(5)
                continue
        else:
            # 链接为空，可能意味着文档正在被编辑或导出失败
            print(f"  导出链接为空 {doc_id}, 稍后重试...")
            time.sleep(5)

    print(f"  多次尝试后仍无法获取导出链接或下载文件 {doc_id}")
    return None, None


def sanitize_filesystem_name(name):
    """清理文件名，使其能在文件系统中安全使用"""
    if not isinstance(name, str):
        name = str(name)
    # 移除Windows和Unix系统不允许的字符
    name = re.sub(r'[<>:"/\\|?*\x00-\x1f\x7f]', '_', name)
    # 移除文件名开头或结尾的点和空格
    name = name.strip('. ')
    if not name:
        return "未命名"
    return name


def process_and_save_node(node_data, current_parent_path, executor):
    """
    递归处理并保存节点数据，使用线程池执行下载任务
    """
    title = node_data.get('title', '未命名文档')
    doc_url = node_data.get('url')
    doc_id = node_data.get('doc_id')
    children = node_data.get('children', [])

    sanitized_title = sanitize_filesystem_name(title)

    # 如果节点没有doc_id，它可能是一个目录节点
    if not doc_id:
        if children:
            # 创建目录
            directory_path = os.path.join(current_parent_path, sanitized_title)
            try:
                os.makedirs(directory_path, exist_ok=True)
                print(f"创建目录: '{directory_path}'")
            except OSError as e:
                print(f"错误: 创建目录 '{directory_path}' 失败: {e}")
                return  # 目录创建失败，不再处理此分支

            # 递归处理子节点
            for child_node in children:
                process_and_save_node(child_node, directory_path, executor)
        return

    # 如果有子节点，说明这是一个目录，里面包含文档或子目录
    if children:
        directory_path = os.path.join(current_parent_path, sanitized_title)
        try:
            os.makedirs(directory_path, exist_ok=True)
            print(f"创建目录: '{directory_path}'")
        except OSError as e:
            print(f"错误: 创建目录 '{directory_path}' 失败: {e}")
            return  # 目录创建失败，不再处理此分支

        if doc_url:
            print(f"  提交下载目录索引: '{title}' ({doc_url})")
            future = executor.submit(download_single_document_task, doc_url, doc_id, directory_path, True, sanitized_title)

        # 递归处理子节点
        for child_node in children:
            process_and_save_node(child_node, directory_path, executor)

    # 如果没有子节点，说明这是一个单独的文档
    else:
        print(f"  提交下载文档: '{title}' ({doc_url})")
        # 将下载任务提交到线程池
        future = executor.submit(download_single_document_task, doc_url, doc_id, current_parent_path, False, sanitized_title)


def download_single_document_task(doc_url, doc_id, parent_dir, is_directory_index, sanitized_title):
    """
    在线程池中执行单个文档的下载和保存任务
    """
    print(f"开始下载: '{sanitized_title}' (ID: {doc_id})")
    response, doc_type = download_yuque_markdown_content(doc_url, doc_id)

    if response and doc_type:
        if is_directory_index:
            # 如果是目录索引，文件名包含 _index
            base_filename = f"{sanitized_title}_index"
        else:
            base_filename = sanitized_title

        if doc_type == 'Sheet':
            document_filename = f"{base_filename}.xlsx"
        else:
            document_filename = f"{base_filename}.md"

        document_filepath = os.path.join(parent_dir, document_filename)

        try:
            os.makedirs(os.path.dirname(document_filepath), exist_ok=True)

            if doc_type == "Sheet":
                with open(document_filepath, 'wb') as f:
                    # 使用iter_content以流式方式写入，避免大文件内存占用
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
            else:
                with open(document_filepath, 'w', encoding='utf-8') as f:
                    f.write(response.text)

            print(f"    成功保存: '{document_filepath}'")
        except Exception as e:
            print(f"  未知错误保存文件 '{document_filepath}': {e}")
    else:
        print(f"  下载失败或未获取到内容: '{sanitized_title}' (ID: {doc_id})")


if __name__ == "__main__":
    print(f"正在从 '{INPUT_JSON_FILE}' 加载数据...")

    with open(INPUT_JSON_FILE, 'r', encoding='utf-8') as f:
        hierarchical_data_list = json.load(f)

    if not os.path.exists(BASE_DOWNLOAD_DIR):
        os.makedirs(BASE_DOWNLOAD_DIR)
        print(f"下载目录已创建: '{os.path.abspath(BASE_DOWNLOAD_DIR)}'")
    else:
        print(f"下载目录: '{os.path.abspath(BASE_DOWNLOAD_DIR)}'")

    print(f"使用 {MAX_WORKERS} 个并发线程开始下载...")
    start_time = time.time()

    with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        futures = []
        for root_node_data in hierarchical_data_list:
            process_and_save_node(root_node_data, BASE_DOWNLOAD_DIR, executor)

    end_time = time.time()
    print(f"\n所有下载任务已提交并执行完毕。总耗时: {end_time - start_time:.2f} 秒。")
